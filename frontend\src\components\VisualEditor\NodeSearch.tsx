import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  TextField,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Typography,
  Box,
  Chip,
  InputAdornment,
  Divider,
} from '@mui/material';
import {
  Search,
  PlayArrow,
  Help,
  FlashOn,
  Calculate,
  Storage,
  Looks,
} from '@mui/icons-material';
import { nodeTypeConfigs, NodeTypeConfig } from './NodeTypes';

interface NodeSearchProps {
  open: boolean;
  onClose: () => void;
  onNodeSelect: (nodeType: string, position: { x: number; y: number }) => void;
  position: { x: number; y: number };
}

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  trigger: <PlayArrow />,
  condition: <Help />,
  effect: <FlashOn />,
  calculation: <Calculate />,
  variable: <Storage />,
  constant: <Looks />,
};

// 搜索历史管理
const SEARCH_HISTORY_KEY = 'nodeSearchHistory';
const MAX_HISTORY_ITEMS = 10;

const getSearchHistory = (): string[] => {
  try {
    const history = localStorage.getItem(SEARCH_HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch {
    return [];
  }
};

const addToSearchHistory = (query: string) => {
  if (!query.trim()) return;
  
  const history = getSearchHistory();
  const newHistory = [query, ...history.filter(item => item !== query)].slice(0, MAX_HISTORY_ITEMS);
  localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
};

const NodeSearch: React.FC<NodeSearchProps> = ({
  open,
  onClose,
  onNodeSelect,
  position,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredNodes, setFilteredNodes] = useState<NodeTypeConfig[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open) {
      setSearchQuery('');
      setSelectedIndex(0);
      setSearchHistory(getSearchHistory());
      // 延迟聚焦，确保Dialog完全打开
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  useEffect(() => {
    if (!searchQuery.trim()) {
      // 没有搜索词时显示所有节点
      setFilteredNodes(nodeTypeConfigs);
    } else {
      // 模糊搜索
      const query = searchQuery.toLowerCase();
      const filtered = nodeTypeConfigs.filter(node => 
        node.label.toLowerCase().includes(query) ||
        node.description.toLowerCase().includes(query) ||
        node.category.toLowerCase().includes(query) ||
        node.type.toLowerCase().includes(query)
      );
      setFilteredNodes(filtered);
    }
    setSelectedIndex(0);
  }, [searchQuery]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredNodes.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        event.preventDefault();
        if (filteredNodes[selectedIndex]) {
          handleNodeSelect(filteredNodes[selectedIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        onClose();
        break;
    }
  };

  const handleNodeSelect = (nodeConfig: NodeTypeConfig) => {
    addToSearchHistory(searchQuery);
    onNodeSelect(nodeConfig.type, position);
    onClose();
  };

  const handleHistorySelect = (historyItem: string) => {
    setSearchQuery(historyItem);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '70vh',
        }
      }}
    >
      <DialogContent sx={{ p: 0 }}>
        {/* 搜索输入框 */}
        <Box sx={{ p: 2, pb: 1 }}>
          <TextField
            ref={searchInputRef}
            fullWidth
            placeholder="搜索节点类型..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              }
            }}
          />
        </Box>

        {/* 搜索历史 */}
        {!searchQuery && searchHistory.length > 0 && (
          <Box sx={{ px: 2, pb: 1 }}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
              最近搜索
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {searchHistory.slice(0, 5).map((item, index) => (
                <Chip
                  key={index}
                  label={item}
                  size="small"
                  variant="outlined"

                  onClick={() => handleHistorySelect(item)}
                  sx={{ fontSize: 11 }}
                />
              ))}
            </Box>
            <Divider sx={{ mt: 1 }} />
          </Box>
        )}

        {/* 节点列表 */}
        <List sx={{ maxHeight: 400, overflow: 'auto', pt: 0 }}>
          {filteredNodes.length === 0 ? (
            <ListItem>
              <ListItemText
                primary="未找到匹配的节点"
                secondary="尝试使用不同的关键词搜索"
              />
            </ListItem>
          ) : (
            filteredNodes.map((nodeConfig, index) => (
              <ListItemButton
                key={nodeConfig.type}
                selected={index === selectedIndex}
                onClick={() => handleNodeSelect(nodeConfig)}
                sx={{
                  borderRadius: 1,
                  mx: 1,
                  mb: 0.5,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: index === selectedIndex ? 'inherit' : nodeConfig.color,
                    minWidth: 40,
                  }}
                >
                  {iconMap[nodeConfig.type]}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {nodeConfig.label}
                      </Typography>
                      <Chip
                        label={nodeConfig.category}
                        size="small"
                        variant="outlined"
                        sx={{
                          fontSize: 10,
                          height: 20,
                          color: index === selectedIndex ? 'inherit' : 'text.secondary',
                          borderColor: index === selectedIndex ? 'currentColor' : 'divider',
                        }}
                      />
                    </Box>
                  }
                  secondary={
                    <Typography
                      variant="caption"
                      sx={{
                        color: index === selectedIndex ? 'inherit' : 'text.secondary',
                        opacity: index === selectedIndex ? 0.8 : 1,
                      }}
                    >
                      {nodeConfig.description}
                    </Typography>
                  }
                />
              </ListItemButton>
            ))
          )}
        </List>

        {/* 提示信息 */}
        <Box sx={{ p: 2, pt: 1, borderTop: 1, borderColor: 'divider' }}>
          <Typography variant="caption" color="text.secondary">
            使用 ↑↓ 键选择，Enter 确认，Esc 取消
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default NodeSearch;
