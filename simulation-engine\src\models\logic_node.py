"""
逻辑节点相关数据模型
"""

from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class NodeType(str, Enum):
    """逻辑节点类型枚举"""
    TRIGGER = "TRIGGER"
    CONDITION = "CONDITION"
    EFFECT = "EFFECT"
    CALCULATION = "CALCULATION"
    PROBABILITY = "PROBABILITY"
    DELAY = "DELAY"
    VARIABLE = "VARIABLE"
    CONSTANT = "CONSTANT"


class TriggerType(str, Enum):
    """触发器类型枚举"""
    ON_ATTACK = "ON_ATTACK"
    ON_DAMAGE = "ON_DAMAGE"
    ON_HEAL = "ON_HEAL"
    ON_BUFF_APPLY = "ON_BUFF_APPLY"
    ON_BUFF_REMOVE = "ON_BUFF_REMOVE"
    ON_DEATH = "ON_DEATH"
    ON_TURN_START = "ON_TURN_START"
    ON_TURN_END = "ON_TURN_END"
    ON_COOLDOWN_READY = "ON_COOLDOWN_READY"


class PinDefinition(BaseModel):
    """引脚定义"""
    type: str
    required: bool = False
    description: Optional[str] = None


class Position(BaseModel):
    """位置定义"""
    x: float
    y: float


class Connection(BaseModel):
    """连接定义"""
    id: str
    from_node: str = Field(alias="from.nodeId")
    from_pin: str = Field(alias="from.outputPin")
    to_node: str = Field(alias="to.nodeId")
    to_pin: str = Field(alias="to.inputPin")

    class Config:
        allow_population_by_field_name = True


class LogicNode(BaseModel):
    """逻辑节点定义"""
    id: str
    type: NodeType
    name: str
    position: Position
    inputs: Dict[str, PinDefinition] = Field(default_factory=dict)
    outputs: Dict[str, PinDefinition] = Field(default_factory=dict)
    parameters: Dict[str, Any] = Field(default_factory=dict)
    description: Optional[str] = None

    def get_parameter(self, name: str, default: Any = None) -> Any:
        """获取参数值"""
        return self.parameters.get(name, default)

    def set_parameter(self, name: str, value: Any) -> None:
        """设置参数值"""
        self.parameters[name] = value

    def has_input(self, pin_name: str) -> bool:
        """检查是否有指定输入引脚"""
        return pin_name in self.inputs

    def has_output(self, pin_name: str) -> bool:
        """检查是否有指定输出引脚"""
        return pin_name in self.outputs

    class Config:
        """Pydantic配置"""
        use_enum_values = True
