# 节点编辑器使用指南

## 🚀 快速开始

### 启动应用
```bash
cd frontend
npm start
```
应用将在 http://localhost:3000 启动

## 🎯 主要功能

### 1. 双击搜索 ⭐ (新功能)
- **操作**: 在画布空白区域双击
- **功能**: 快速打开节点搜索对话框
- **搜索**: 支持按名称、描述、分类搜索
- **历史**: 自动保存搜索历史
- **快捷键**: 
  - `↑↓` 选择节点
  - `Enter` 确认添加
  - `Esc` 取消

### 2. 现代化节点面板 (左侧)
- **全部**: 显示所有可用节点，按分类组织
- **收藏**: 点击节点卡片上的 ⭐ 图标收藏常用节点
- **最近**: 自动记录最近使用的节点
- **搜索**: 面板顶部的搜索框可过滤节点
- **拖拽**: 将节点拖拽到画布创建新节点

### 3. 工具栏功能 (顶部)
- **文件操作**: 保存、导入、导出
- **编辑操作**: 撤销、重做、全选、复制、粘贴、删除
- **视图控制**: 缩放、适应视图、网格开关、小地图开关
- **对齐工具**: 水平对齐、垂直对齐 (需选中多个节点)
- **执行控制**: 开始/停止执行

### 4. 属性面板 (右侧)
- **节点信息**: 显示节点类型、ID、描述
- **基本属性**: 编辑节点名称和描述
- **节点配置**: 根据节点类型显示特定配置选项
- **高级设置**: 启用/禁用节点、调试模式等
- **节点操作**: 复制、删除节点按钮

## 🎮 操作技巧

### 快捷键
- `Ctrl+S`: 保存
- `Ctrl+Z`: 撤销
- `Ctrl+Y`: 重做
- `Ctrl+A`: 全选
- `Delete`: 删除选中节点
- `双击画布`: 打开搜索

### 节点操作
1. **创建节点**: 
   - 拖拽从节点面板
   - 双击画布搜索
2. **选择节点**: 点击节点
3. **连接节点**: 拖拽节点的输出引脚到另一个节点的输入引脚
4. **编辑属性**: 选中节点后在右侧属性面板编辑
5. **删除节点**: 选中后按Delete键或使用属性面板的删除按钮

### 视图操作
- **缩放**: 鼠标滚轮或工具栏缩放按钮
- **平移**: 拖拽画布空白区域
- **适应视图**: 工具栏的适应视图按钮
- **网格**: 工具栏切换网格显示
- **小地图**: 工具栏切换小地图显示

## 🎨 节点类型

### 事件类 (Events)
- **触发器**: 定义事件触发条件

### 逻辑类 (Logic)  
- **条件**: 条件判断节点

### 动作类 (Actions)
- **效果**: 执行具体效果

### 数学类 (Math)
- **计算**: 数值计算节点

### 数据类 (Data)
- **变量**: 获取变量值
- **常量**: 常量值

## 🔧 高级功能

### 节点配置示例

#### 触发器节点
- 触发类型: 攻击时、受伤时、治疗时等
- 可配置具体的触发条件

#### 条件节点
- 条件类型: 属性比较、标签检查、概率判断
- 属性比较: 设置属性名、操作符、比较值
- 概率判断: 设置概率百分比

#### 效果节点
- 效果类型: 造成伤害、治疗、施加Buff等
- 数值设置: 配置效果的具体数值

#### 计算节点
- 运算类型: 加法、减法、乘法、除法、幂运算等
- 支持最小值、最大值计算

### 数值输入
- **数字输入框**: 直接输入数值
- **滑块**: 某些数值支持滑块调节
- **范围限制**: 自动限制数值在合理范围内

## 💡 使用建议

1. **善用搜索**: 双击画布比拖拽更快
2. **收藏常用**: 将常用节点加入收藏
3. **合理命名**: 给节点起有意义的名称
4. **保存习惯**: 经常保存工作进度
5. **测试执行**: 使用执行功能测试逻辑

## 🐛 常见问题

### Q: 双击没有反应？
A: 确保双击的是画布空白区域，不是节点上

### Q: 节点连接失败？
A: 检查引脚类型是否兼容，执行引脚只能连接执行引脚

### Q: 找不到某个节点？
A: 使用搜索功能，支持模糊搜索

### Q: 属性修改没有生效？
A: 确保点击了保存按钮或使用Ctrl+S保存

## 🔮 即将推出

- 主题切换 (深色/浅色)
- 自定义节点类型
- 节点模板系统
- 协作编辑功能
- 版本控制

---

享受现代化的节点编辑体验！如有问题请反馈。
