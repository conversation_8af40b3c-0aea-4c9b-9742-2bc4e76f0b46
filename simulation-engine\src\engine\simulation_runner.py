"""
模拟运行器
协调游戏循环、事件系统和状态管理器来运行完整的模拟
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from ..models.simulation import Simulation, SimulationResult, SimulationStatus, DataPoint, SimulationEvent
from ..models.entity import Entity
from .game_loop import GameLoop
from .event_system import EventSystem, EventType, Event
from .state_manager import StateManager


class SimulationRunner:
    """模拟运行器"""

    def __init__(self):
        self.event_system = EventSystem()
        self.state_manager = StateManager(self.event_system)
        self.game_loop = GameLoop(self.event_system, self.state_manager)
        
        self._current_simulation: Optional[Simulation] = None
        self._current_result: Optional[SimulationResult] = None
        self._data_collectors: Dict[str, Callable[[float], None]] = {}
        
        # 设置基础事件监听
        self._setup_event_listeners()

    def _setup_event_listeners(self) -> None:
        """设置基础事件监听器"""
        # 监听属性变化事件，收集数据点
        self.event_system.subscribe(EventType.ATTRIBUTE_CHANGE, self._on_attribute_change)
        
        # 监听其他重要事件
        self.event_system.subscribe(EventType.DAMAGE, self._on_damage)
        self.event_system.subscribe(EventType.HEAL, self._on_heal)
        self.event_system.subscribe(EventType.DEATH, self._on_death)

    def _on_attribute_change(self, event: Event) -> None:
        """处理属性变化事件"""
        if not self._current_result:
            return
            
        data_point = DataPoint(
            timestamp=event.timestamp,
            entity_id=event.source_id or "",
            attribute=event.data.get("attribute", ""),
            value=event.data.get("new_value", 0.0)
        )
        self._current_result.data_points.append(data_point)

    def _on_damage(self, event: Event) -> None:
        """处理伤害事件"""
        if not self._current_result:
            return
            
        sim_event = SimulationEvent(
            id=event.id,
            timestamp=event.timestamp,
            type="damage",
            source_id=event.source_id,
            target_id=event.target_id,
            data=event.data,
            description=event.description
        )
        self._current_result.events.append(sim_event)

    def _on_heal(self, event: Event) -> None:
        """处理治疗事件"""
        if not self._current_result:
            return
            
        sim_event = SimulationEvent(
            id=event.id,
            timestamp=event.timestamp,
            type="heal",
            source_id=event.source_id,
            target_id=event.target_id,
            data=event.data,
            description=event.description
        )
        self._current_result.events.append(sim_event)

    def _on_death(self, event: Event) -> None:
        """处理死亡事件"""
        if not self._current_result:
            return
            
        sim_event = SimulationEvent(
            id=event.id,
            timestamp=event.timestamp,
            type="death",
            source_id=event.source_id,
            target_id=event.target_id,
            data=event.data,
            description=event.description
        )
        self._current_result.events.append(sim_event)

    async def run_simulation(self, simulation: Simulation) -> SimulationResult:
        """运行模拟"""
        print(f"Starting simulation: {simulation.name}")
        
        # 设置当前模拟
        self._current_simulation = simulation
        
        # 创建模拟结果
        self._current_result = SimulationResult(
            id=str(uuid.uuid4()),
            simulation_id=simulation.id,
            start_time=datetime.now(),
            status=SimulationStatus.RUNNING
        )
        
        try:
            # 重置引擎状态
            self.game_loop.reset()
            
            # 设置tick频率
            self.game_loop.set_tick_rate(simulation.settings.tick_rate)
            
            # 加载实体到状态管理器
            for entity in simulation.entities:
                self.state_manager.add_entity(entity)
            
            # 设置基础AI行为（简单的DPS测试）
            self._setup_basic_ai()
            
            # 运行游戏循环
            await self.game_loop.start(duration=simulation.settings.duration)
            
            # 计算结果摘要
            self._calculate_summary()
            
            # 标记完成
            self._current_result.status = SimulationStatus.COMPLETED
            self._current_result.end_time = datetime.now()
            
            print(f"Simulation completed: {simulation.name}")
            
        except Exception as e:
            print(f"Simulation failed: {e}")
            self._current_result.status = SimulationStatus.FAILED
            self._current_result.error = str(e)
            self._current_result.end_time = datetime.now()
        
        return self._current_result

    def _setup_basic_ai(self) -> None:
        """设置基础AI行为（用于DPS测试）"""
        # 添加简单的攻击AI
        self.game_loop.add_fixed_update_callback(self._basic_attack_ai)

    def _basic_attack_ai(self, delta_time: float) -> None:
        """基础攻击AI逻辑"""
        if not self._current_simulation:
            return

        # 获取所有角色和怪物
        characters = self.state_manager.get_entities_by_type("CHARACTER")
        monsters = self.state_manager.get_entities_by_type("MONSTER")

        # 角色攻击怪物
        for character in characters:
            if not self.state_manager.is_alive(character.id):
                continue

            # 检查攻击冷却
            last_attack_time = self.state_manager.get_state(character.id, "last_attack_time", 0.0)
            attack_speed = character.get_attribute_value("attack_speed") or 1.0
            attack_interval = 1.0 / attack_speed

            current_time = self.game_loop.get_current_time()
            if current_time - last_attack_time >= attack_interval:
                # 寻找目标
                target = self._find_nearest_enemy(character, monsters)
                if target and self.state_manager.is_alive(target.id):
                    self._perform_attack(character, target)
                    self.state_manager.set_state(character.id, "last_attack_time", current_time)

    def _find_nearest_enemy(self, attacker: Entity, enemies: List[Entity]) -> Optional[Entity]:
        """寻找最近的敌人（简化版本）"""
        for enemy in enemies:
            if self.state_manager.is_alive(enemy.id):
                return enemy
        return None

    def _perform_attack(self, attacker: Entity, target: Entity) -> None:
        """执行攻击"""
        # 计算伤害
        attack_power = attacker.get_attribute_value("attack_power") or 100.0
        damage = attack_power

        # 应用伤害
        current_hp = self.state_manager.get_attribute_value(target.id, "health")
        new_hp = max(0, current_hp - damage)
        self.state_manager.set_attribute_value(target.id, "health", new_hp)

        # 发出伤害事件
        self.event_system.create_and_emit(
            EventType.DAMAGE,
            source_id=attacker.id,
            target_id=target.id,
            data={
                "damage": damage,
                "remaining_hp": new_hp
            },
            description=f"{attacker.name} attacks {target.name} for {damage} damage"
        )

    def _calculate_summary(self) -> None:
        """计算模拟结果摘要"""
        if not self._current_result:
            return

        # 计算总伤害和DPS
        damage_events = [e for e in self._current_result.events if e.type == "damage"]
        total_damage = sum(e.data.get("damage", 0) for e in damage_events)

        duration = self.game_loop.get_current_time()
        dps = total_damage / duration if duration > 0 else 0

        # 计算总治疗和HPS
        heal_events = [e for e in self._current_result.events if e.type == "heal"]
        total_healing = sum(e.data.get("healing", 0) for e in heal_events)
        hps = total_healing / duration if duration > 0 else 0

        # 计算生存时间
        death_events = [e for e in self._current_result.events if e.type == "death"]
        survival_time = duration
        if death_events:
            first_death = min(e.timestamp for e in death_events)
            survival_time = first_death

        # 更新摘要
        self._current_result.summary.total_damage = total_damage
        self._current_result.summary.dps = dps
        self._current_result.summary.total_healing = total_healing
        self._current_result.summary.hps = hps
        self._current_result.summary.survival_time = survival_time

        print(f"Simulation Summary:")
        print(f"  Total Damage: {total_damage:.2f}")
        print(f"  DPS: {dps:.2f}")
        print(f"  Total Healing: {total_healing:.2f}")
        print(f"  HPS: {hps:.2f}")
        print(f"  Survival Time: {survival_time:.2f}s")

    def get_current_result(self) -> Optional[SimulationResult]:
        """获取当前模拟结果"""
        return self._current_result

    def is_running(self) -> bool:
        """检查是否正在运行模拟"""
        return self.game_loop.is_running()

    def stop_simulation(self) -> None:
        """停止当前模拟"""
        self.game_loop.stop()
        if self._current_result and self._current_result.status == SimulationStatus.RUNNING:
            self._current_result.status = SimulationStatus.CANCELLED
            self._current_result.end_time = datetime.now()
