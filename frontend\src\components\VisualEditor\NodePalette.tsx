import React from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
} from '@mui/material';
import {
  ExpandMore,
  PlayArrow,
  Help,
  FlashOn,
  Calculate,
  Storage,
  Looks,
} from '@mui/icons-material';
import { getNodeTypesByCategory } from './NodeTypes';

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  trigger: <PlayArrow />,
  condition: <Help />,
  effect: <FlashOn />,
  calculation: <Calculate />,
  variable: <Storage />,
  constant: <Looks />,
};

const NodePalette: React.FC = () => {
  const nodesByCategory = getNodeTypesByCategory();

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Box>
      {Object.entries(nodesByCategory).map(([category, nodes]) => (
        <Accordion key={category} defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle2">{category}</Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 0 }}>
            <List dense>
              {nodes.map((nodeConfig) => (
                <ListItem
                  key={nodeConfig.type}
                  component={Paper}
                  elevation={1}
                  sx={{
                    mb: 1,
                    cursor: 'grab',
                    '&:hover': {
                      backgroundColor: 'action.hover',
                    },
                    '&:active': {
                      cursor: 'grabbing',
                    },
                  }}
                  draggable
                  onDragStart={(e) => onDragStart(e, nodeConfig.type)}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 36,
                      color: nodeConfig.color,
                    }}
                  >
                    {iconMap[nodeConfig.type]}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ fontSize: 12 }}>
                        {nodeConfig.label}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" sx={{ fontSize: 10 }}>
                        {nodeConfig.description}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}
      
      <Box sx={{ mt: 2, p: 1, backgroundColor: 'action.hover', borderRadius: 1 }}>
        <Typography variant="caption" sx={{ fontSize: 10 }}>
          💡 拖拽节点到画布中创建新节点
        </Typography>
      </Box>
    </Box>
  );
};

export default NodePalette;
