/**
 * Game Logic Preview - Backend Server
 * Main entry point for the Node.js backend server
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

import { connectDatabase } from './utils/database';
import { errorHandler } from './utils/errorHandler';
import { logger } from './utils/logger';

// Import routes
import entityRoutes from './routes/entities';
import simulationRoutes from './routes/simulations';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'game-logic-preview-backend',
    version: '0.1.0',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/entities', entityRoutes);
app.use('/api/simulations', simulationRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on('join-simulation', (simulationId: string) => {
    socket.join(`simulation-${simulationId}`);
    logger.info(`Client ${socket.id} joined simulation ${simulationId}`);
  });

  socket.on('leave-simulation', (simulationId: string) => {
    socket.leave(`simulation-${simulationId}`);
    logger.info(`Client ${socket.id} left simulation ${simulationId}`);
  });

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
async function startServer() {
  try {
    // Try to connect to database, but don't fail if it's not available
    try {
      await connectDatabase();
      logger.info('Connected to MongoDB');
    } catch (dbError) {
      logger.warn('MongoDB not available, running without database:', dbError);
    }

    // Start server
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
  });
});

// Export io for use in other modules
export { io };

// Start the server
startServer();
