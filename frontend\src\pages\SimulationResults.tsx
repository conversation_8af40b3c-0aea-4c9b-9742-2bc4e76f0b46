import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';

import { Simulation, SimulationResult } from '../types';
import { simulationApi } from '../services/api';

const SimulationResults: React.FC = () => {
  const [simulations, setSimulations] = useState<Simulation[]>([]);
  const [selectedSimulation, setSelectedSimulation] = useState<Simulation | null>(null);
  const [results, setResults] = useState<SimulationResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingResults, setLoadingResults] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载模拟列表
  const loadSimulations = async () => {
    try {
      setLoading(true);
      const simulationList = await simulationApi.getAll();
      setSimulations(simulationList);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load simulations');
    } finally {
      setLoading(false);
    }
  };

  // 加载模拟结果
  const loadResults = async (simulationId: string) => {
    try {
      setLoadingResults(true);
      const simulationResults = await simulationApi.getResults(simulationId);
      setResults(simulationResults);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load simulation results');
      setResults([]);
    } finally {
      setLoadingResults(false);
    }
  };

  useEffect(() => {
    loadSimulations();
  }, []);

  // 选择模拟
  const handleSelectSimulation = (simulation: Simulation) => {
    setSelectedSimulation(simulation);
    loadResults(simulation.id);
  };

  // 格式化时间
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
  };

  // 格式化数字
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(1);
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Simulation Results
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* 模拟列表 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Simulations
            </Typography>
            
            {simulations.length === 0 ? (
              <Typography color="text.secondary">
                No simulations found. Create and run a simulation first.
              </Typography>
            ) : (
              <List dense>
                {simulations.map((simulation) => (
                  <ListItem
                    key={simulation.id}
                    button
                    selected={selectedSimulation?.id === simulation.id}
                    onClick={() => handleSelectSimulation(simulation)}
                  >
                    <ListItemText
                      primary={simulation.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {simulation.entities.length} entities
                          </Typography>
                          <Typography variant="caption" display="block">
                            Duration: {formatDuration(simulation.settings.duration)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* 结果详情 */}
        <Grid item xs={12} md={8}>
          {!selectedSimulation ? (
            <Paper sx={{ p: 2 }}>
              <Typography color="text.secondary">
                Select a simulation to view results
              </Typography>
            </Paper>
          ) : (
            <Box>
              {/* 模拟信息 */}
              <Paper sx={{ p: 2, mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedSimulation.name}
                </Typography>
                {selectedSimulation.description && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {selectedSimulation.description}
                  </Typography>
                )}
                
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Settings:
                  </Typography>
                  <Typography variant="body2">
                    Duration: {formatDuration(selectedSimulation.settings.duration)} |
                    Tick Rate: {selectedSimulation.settings.tickRate}/s |
                    Log Level: {selectedSimulation.settings.logLevel}
                  </Typography>
                </Box>

                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Entities ({selectedSimulation.entities.length}):
                  </Typography>
                  <Box>
                    {selectedSimulation.entities.map((entity) => (
                      <Chip
                        key={entity.id}
                        label={`${entity.name} (${entity.type})`}
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </Box>
                </Box>
              </Paper>

              {/* 结果列表 */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Results
                </Typography>
                
                {loadingResults ? (
                  <Box display="flex" justifyContent="center" p={2}>
                    <CircularProgress />
                  </Box>
                ) : results.length === 0 ? (
                  <Typography color="text.secondary">
                    No results available for this simulation.
                  </Typography>
                ) : (
                  <Grid container spacing={2}>
                    {results.map((result) => (
                      <Grid item xs={12} key={result.id}>
                        <Card variant="outlined">
                          <CardContent>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                              <Typography variant="h6">
                                Result {result.id.slice(-8)}
                              </Typography>
                              <Chip
                                label={result.status}
                                color={getStatusColor(result.status) as any}
                                size="small"
                              />
                            </Box>

                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Started: {new Date(result.startTime).toLocaleString()}
                              {result.endTime && (
                                <> | Ended: {new Date(result.endTime).toLocaleString()}</>
                              )}
                            </Typography>

                            {result.status === 'completed' && result.summary && (
                              <Box sx={{ mt: 2 }}>
                                <Typography variant="subtitle2" gutterBottom>
                                  Summary:
                                </Typography>
                                <Grid container spacing={2}>
                                  {result.summary.total_damage && (
                                    <Grid item xs={6} sm={3}>
                                      <Typography variant="body2">
                                        <strong>Total Damage:</strong><br />
                                        {formatNumber(result.summary.total_damage)}
                                      </Typography>
                                    </Grid>
                                  )}
                                  {result.summary.dps && (
                                    <Grid item xs={6} sm={3}>
                                      <Typography variant="body2">
                                        <strong>DPS:</strong><br />
                                        {formatNumber(result.summary.dps)}
                                      </Typography>
                                    </Grid>
                                  )}
                                  {result.summary.total_healing && (
                                    <Grid item xs={6} sm={3}>
                                      <Typography variant="body2">
                                        <strong>Total Healing:</strong><br />
                                        {formatNumber(result.summary.total_healing)}
                                      </Typography>
                                    </Grid>
                                  )}
                                  {result.summary.survival_time && (
                                    <Grid item xs={6} sm={3}>
                                      <Typography variant="body2">
                                        <strong>Survival Time:</strong><br />
                                        {formatDuration(result.summary.survival_time)}
                                      </Typography>
                                    </Grid>
                                  )}
                                </Grid>
                              </Box>
                            )}

                            {result.error && (
                              <Alert severity="error" sx={{ mt: 2 }}>
                                {result.error}
                              </Alert>
                            )}

                            <Box sx={{ mt: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                Events: {result.events.length} | Data Points: {result.dataPoints.length}
                              </Typography>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Paper>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SimulationResults;
