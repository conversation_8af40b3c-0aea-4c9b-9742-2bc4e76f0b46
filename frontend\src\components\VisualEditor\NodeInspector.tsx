import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider,
  Chip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { Node } from 'reactflow';
import { Save, PlayArrow, Stop } from '@mui/icons-material';
import { NodeData } from './NodeTypes';
import { ExecutionState } from './ExecutionEngine';

interface NodeInspectorProps {
  selectedNode: Node<NodeData> | null;
  onNodeUpdate: (nodeId: string, newData: Partial<NodeData>) => void;
  onSave: () => void;
  onExecute?: () => void;
  onStopExecution?: () => void;
  isExecuting?: boolean;
  executionStates?: Map<string, ExecutionState>;
}

const NodeInspector: React.FC<NodeInspectorProps> = ({
  selectedNode,
  onNodeUpdate,
  onSave,
  onExecute,
  onStopExecution,
  isExecuting = false,
  executionStates,
}) => {
  const [localData, setLocalData] = useState<NodeData | null>(null);

  useEffect(() => {
    if (selectedNode) {
      setLocalData({ ...selectedNode.data });
    } else {
      setLocalData(null);
    }
  }, [selectedNode]);

  const handleDataChange = (key: string, value: any) => {
    if (!localData || !selectedNode) return;

    const newData = { ...localData, [key]: value };
    setLocalData(newData);
    onNodeUpdate(selectedNode.id, { [key]: value });
  };

  const renderNodeSpecificFields = () => {
    if (!selectedNode || !localData) return null;

    switch (selectedNode.type) {
      case 'trigger':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>触发类型</InputLabel>
              <Select
                value={localData.triggerType || 'ON_ATTACK'}
                onChange={(e) => handleDataChange('triggerType', e.target.value)}
                label="触发类型"
              >
                <MenuItem value="ON_ATTACK">攻击时</MenuItem>
                <MenuItem value="ON_DAMAGE">受伤时</MenuItem>
                <MenuItem value="ON_HEAL">治疗时</MenuItem>
                <MenuItem value="ON_BUFF_APPLY">Buff生效时</MenuItem>
                <MenuItem value="ON_BUFF_REMOVE">Buff移除时</MenuItem>
                <MenuItem value="ON_DEATH">死亡时</MenuItem>
                <MenuItem value="ON_TURN_START">回合开始</MenuItem>
                <MenuItem value="ON_TURN_END">回合结束</MenuItem>
              </Select>
            </FormControl>
          </Box>
        );

      case 'condition':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>条件类型</InputLabel>
              <Select
                value={localData.conditionType || 'ATTRIBUTE_COMPARE'}
                onChange={(e) => handleDataChange('conditionType', e.target.value)}
                label="条件类型"
              >
                <MenuItem value="ATTRIBUTE_COMPARE">属性比较</MenuItem>
                <MenuItem value="HAS_TAG">拥有标签</MenuItem>
                <MenuItem value="PROBABILITY">概率</MenuItem>
                <MenuItem value="COOLDOWN_READY">冷却就绪</MenuItem>
              </Select>
            </FormControl>

            {localData.conditionType === 'ATTRIBUTE_COMPARE' && (
              <>
                <TextField
                  fullWidth
                  margin="normal"
                  size="small"
                  label="属性名"
                  value={localData.attribute || ''}
                  onChange={(e) => handleDataChange('attribute', e.target.value)}
                />
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>操作符</InputLabel>
                  <Select
                    value={localData.operator || '>'}
                    onChange={(e) => handleDataChange('operator', e.target.value)}
                    label="操作符"
                  >
                    <MenuItem value=">">&gt;</MenuItem>
                    <MenuItem value=">=">&gt;=</MenuItem>
                    <MenuItem value="<">&lt;</MenuItem>
                    <MenuItem value="<=">&lt;=</MenuItem>
                    <MenuItem value="==">=</MenuItem>
                    <MenuItem value="!=">!=</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  fullWidth
                  margin="normal"
                  size="small"
                  label="比较值"
                  type="number"
                  value={localData.value || 0}
                  onChange={(e) => handleDataChange('value', Number(e.target.value))}
                />
              </>
            )}

            {localData.conditionType === 'HAS_TAG' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="标签名"
                value={localData.tagName || ''}
                onChange={(e) => handleDataChange('tagName', e.target.value)}
              />
            )}

            {localData.conditionType === 'PROBABILITY' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="概率 (0-1)"
                type="number"
                inputProps={{ min: 0, max: 1, step: 0.01 }}
                value={localData.probability || 0.5}
                onChange={(e) => handleDataChange('probability', Number(e.target.value))}
              />
            )}
          </Box>
        );

      case 'effect':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>效果类型</InputLabel>
              <Select
                value={localData.effectType || 'DAMAGE'}
                onChange={(e) => handleDataChange('effectType', e.target.value)}
                label="效果类型"
              >
                <MenuItem value="DAMAGE">伤害</MenuItem>
                <MenuItem value="HEAL">治疗</MenuItem>
                <MenuItem value="BUFF_APPLY">施加Buff</MenuItem>
                <MenuItem value="BUFF_REMOVE">移除Buff</MenuItem>
                <MenuItem value="ATTRIBUTE_MODIFY">修改属性</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="数值"
              type="number"
              value={localData.amount || 0}
              onChange={(e) => handleDataChange('amount', Number(e.target.value))}
            />

            {(localData.effectType === 'BUFF_APPLY' || localData.effectType === 'BUFF_REMOVE') && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="Buff ID"
                value={localData.buffId || ''}
                onChange={(e) => handleDataChange('buffId', e.target.value)}
              />
            )}

            {localData.effectType === 'ATTRIBUTE_MODIFY' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="属性名"
                value={localData.attributeName || ''}
                onChange={(e) => handleDataChange('attributeName', e.target.value)}
              />
            )}
          </Box>
        );

      case 'calculation':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>运算类型</InputLabel>
              <Select
                value={localData.operation || 'ADD'}
                onChange={(e) => handleDataChange('operation', e.target.value)}
                label="运算类型"
              >
                <MenuItem value="ADD">加法 (+)</MenuItem>
                <MenuItem value="SUBTRACT">减法 (-)</MenuItem>
                <MenuItem value="MULTIPLY">乘法 (×)</MenuItem>
                <MenuItem value="DIVIDE">除法 (÷)</MenuItem>
                <MenuItem value="POWER">幂运算 (^)</MenuItem>
                <MenuItem value="MIN">最小值</MenuItem>
                <MenuItem value="MAX">最大值</MenuItem>
              </Select>
            </FormControl>
          </Box>
        );

      case 'variable':
        return (
          <Box>
            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="变量名"
              value={localData.variableName || ''}
              onChange={(e) => handleDataChange('variableName', e.target.value)}
            />
            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="实体ID"
              value={localData.entityId || 'self'}
              onChange={(e) => handleDataChange('entityId', e.target.value)}
              helperText="使用 'self' 表示当前实体，'target' 表示目标实体"
            />
          </Box>
        );

      case 'constant':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>值类型</InputLabel>
              <Select
                value={localData.valueType || 'number'}
                onChange={(e) => handleDataChange('valueType', e.target.value)}
                label="值类型"
              >
                <MenuItem value="number">数字</MenuItem>
                <MenuItem value="string">字符串</MenuItem>
                <MenuItem value="boolean">布尔值</MenuItem>
              </Select>
            </FormControl>

            {localData.valueType === 'number' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="数值"
                type="number"
                value={localData.value || 0}
                onChange={(e) => handleDataChange('value', Number(e.target.value))}
              />
            )}

            {localData.valueType === 'string' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="字符串值"
                value={localData.value || ''}
                onChange={(e) => handleDataChange('value', e.target.value)}
              />
            )}

            {localData.valueType === 'boolean' && (
              <FormControlLabel
                control={
                  <Switch
                    checked={localData.value || false}
                    onChange={(e) => handleDataChange('value', e.target.checked)}
                  />
                }
                label="布尔值"
              />
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  if (!selectedNode) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="body2" color="text.secondary">
          选择一个节点来编辑其属性
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* 节点基本信息 */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          节点信息
        </Typography>
        <Chip
          label={selectedNode.type}
          size="small"
          color="primary"
          sx={{ mb: 1 }}
        />
        <Typography variant="caption" display="block" color="text.secondary">
          ID: {selectedNode.id}
        </Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* 基本属性 */}
      <TextField
        fullWidth
        margin="normal"
        size="small"
        label="节点名称"
        value={localData?.label || ''}
        onChange={(e) => handleDataChange('label', e.target.value)}
      />

      <TextField
        fullWidth
        margin="normal"
        size="small"
        label="描述"
        multiline
        rows={2}
        value={localData?.description || ''}
        onChange={(e) => handleDataChange('description', e.target.value)}
      />

      <Divider sx={{ my: 2 }} />

      {/* 节点特定属性 */}
      <Typography variant="subtitle2" gutterBottom>
        节点属性
      </Typography>
      {renderNodeSpecificFields()}

      <Divider sx={{ my: 2 }} />

      {/* 执行控制 */}
      <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
        <Button
          variant="contained"
          color={isExecuting ? "error" : "success"}
          startIcon={isExecuting ? <Stop /> : <PlayArrow />}
          onClick={isExecuting ? onStopExecution : onExecute}
          disabled={!onExecute}
          sx={{ flex: 1 }}
        >
          {isExecuting ? '停止执行' : '开始执行'}
        </Button>

        <Button
          variant="outlined"
          startIcon={<Save />}
          onClick={onSave}
          sx={{ flex: 1 }}
        >
          保存
        </Button>
      </Box>

      {/* 执行状态显示 */}
      {selectedNode && executionStates && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            执行状态
          </Typography>
          {(() => {
            const state = executionStates.get(selectedNode.id);
            if (!state) return null;

            return (
              <Box>
                <Chip
                  label={state.status}
                  color={
                    state.status === 'executing' ? 'info' :
                    state.status === 'completed' ? 'success' :
                    state.status === 'error' ? 'error' : 'default'
                  }
                  size="small"
                  sx={{ mb: 1 }}
                />

                {state.status === 'executing' && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" display="block">
                      进度: {state.progress.toFixed(0)}%
                    </Typography>
                    <Box
                      sx={{
                        width: '100%',
                        height: 4,
                        backgroundColor: 'grey.300',
                        borderRadius: 2,
                        overflow: 'hidden',
                        mt: 0.5,
                      }}
                    >
                      <Box
                        sx={{
                          width: `${state.progress}%`,
                          height: '100%',
                          backgroundColor: 'primary.main',
                          transition: 'width 0.3s ease',
                        }}
                      />
                    </Box>
                  </Box>
                )}

                {state.output && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" display="block">
                      输出: {JSON.stringify(state.output)}
                    </Typography>
                  </Box>
                )}

                {state.error && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" color="error" display="block">
                      错误: {state.error}
                    </Typography>
                  </Box>
                )}
              </Box>
            );
          })()}
        </Box>
      )}
    </Box>
  );
};

export default NodeInspector;
