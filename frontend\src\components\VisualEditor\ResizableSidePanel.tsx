import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Slide,
  Divider,
} from '@mui/material';
import {
  Assessment,
  Close,
  ChevronLeft,
  ChevronRight,
  DragIndicator,
} from '@mui/icons-material';

interface ResizableSidePanelProps {
  children: React.ReactNode;
  open: boolean;
  onClose: () => void;
  title?: string;
  defaultWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  side?: 'left' | 'right';
}

const ResizableSidePanel: React.FC<ResizableSidePanelProps> = ({
  children,
  open,
  onClose,
  title = "侧边面板",
  defaultWidth = 400,
  minWidth = 250,
  maxWidth = 800,
  side = 'right',
}) => {
  const [width, setWidth] = useState(defaultWidth);
  const [isResizing, setIsResizing] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);

  // 开始拖拽调整大小
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = width;
    
    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [width]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = side === 'right' ? startXRef.current - e.clientX : e.clientX - startXRef.current;
    const newWidth = Math.min(maxWidth, Math.max(minWidth, startWidthRef.current + deltaX));
    setWidth(newWidth);
  }, [isResizing, side, minWidth, maxWidth]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [handleMouseMove]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [handleMouseMove, handleMouseUp]);

  // 保存宽度到localStorage
  useEffect(() => {
    localStorage.setItem('sidePanelWidth', width.toString());
  }, [width]);

  // 从localStorage恢复宽度
  useEffect(() => {
    const savedWidth = localStorage.getItem('sidePanelWidth');
    if (savedWidth) {
      const parsedWidth = parseInt(savedWidth, 10);
      if (parsedWidth >= minWidth && parsedWidth <= maxWidth) {
        setWidth(parsedWidth);
      }
    }
  }, [minWidth, maxWidth]);

  const panelStyle = {
    width: `${width}px`,
    height: '100%',
    position: 'fixed' as const,
    top: 0,
    [side]: 0,
    zIndex: 1300,
    display: 'flex',
    flexDirection: 'column' as const,
    backgroundColor: 'background.paper',
    borderLeft: side === 'right' ? '1px solid' : 'none',
    borderRight: side === 'left' ? '1px solid' : 'none',
    borderColor: 'divider',
    boxShadow: side === 'right' ? '-2px 0 8px rgba(0,0,0,0.1)' : '2px 0 8px rgba(0,0,0,0.1)',
  };

  const resizerStyle = {
    position: 'absolute' as const,
    top: 0,
    [side === 'right' ? 'left' : 'right']: 0,
    width: '4px',
    height: '100%',
    cursor: 'col-resize',
    backgroundColor: isResizing ? 'primary.main' : 'transparent',
    '&:hover': {
      backgroundColor: 'primary.light',
    },
    transition: 'background-color 0.2s ease',
  };

  return (
    <>
      {/* 背景遮罩 */}
      {open && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            zIndex: 1200,
          }}
          onClick={onClose}
        />
      )}

      {/* 侧滑面板 */}
      <Slide direction={side === 'right' ? 'left' : 'right'} in={open} mountOnEnter unmountOnExit>
        <Paper ref={panelRef} sx={panelStyle}>
          {/* 调整大小手柄 */}
          <Box
            sx={resizerStyle}
            onMouseDown={handleMouseDown}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                color: 'text.secondary',
                opacity: isResizing ? 1 : 0.5,
                transition: 'opacity 0.2s ease',
              }}
            >
              <DragIndicator sx={{ fontSize: 16, transform: 'rotate(90deg)' }} />
            </Box>
          </Box>

          {/* 标题栏 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              p: 2,
              borderBottom: 1,
              borderColor: 'divider',
              backgroundColor: 'background.default',
              flexShrink: 0,
            }}
          >
            <Assessment sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" sx={{ flex: 1, fontWeight: 'medium', fontSize: '1.1rem' }}>
              {title}
            </Typography>
            
            {/* 宽度指示器 */}
            <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
              {width}px
            </Typography>
            
            <Tooltip title="关闭">
              <IconButton size="small" onClick={onClose}>
                <Close />
              </IconButton>
            </Tooltip>
          </Box>

          {/* 内容区域 */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              position: 'relative',
            }}
          >
            {children}
          </Box>

          {/* 底部状态栏 */}
          <Box
            sx={{
              p: 1,
              borderTop: 1,
              borderColor: 'divider',
              backgroundColor: 'action.hover',
              flexShrink: 0,
            }}
          >
            <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
              <DragIndicator sx={{ fontSize: 12, mr: 0.5, transform: 'rotate(90deg)' }} />
              拖拽左侧边缘调整宽度
            </Typography>
          </Box>
        </Paper>
      </Slide>
    </>
  );
};

export default ResizableSidePanel;
