"""
模拟相关数据模型
"""

from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

from .entity import Entity
from .logic_node import Connection


class SimulationStatus(str, Enum):
    """模拟状态枚举"""
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARN = "warn"
    ERROR = "error"


class SimulationSettings(BaseModel):
    """模拟设置"""
    duration: float  # 模拟持续时间（秒）
    tick_rate: float = 60.0  # 每秒tick数
    random_seed: Optional[int] = None  # 随机种子
    log_level: LogLevel = LogLevel.INFO
    enable_real_time_updates: bool = True


class DataPoint(BaseModel):
    """模拟结果数据点"""
    timestamp: float
    entity_id: str
    attribute: str
    value: float


class SimulationEvent(BaseModel):
    """模拟事件"""
    id: str
    timestamp: float
    type: str
    source_id: Optional[str] = None
    target_id: Optional[str] = None
    data: Dict[str, Any] = Field(default_factory=dict)
    description: str


class SimulationSummary(BaseModel):
    """模拟结果摘要"""
    total_damage: Optional[float] = None
    dps: Optional[float] = None
    total_healing: Optional[float] = None
    hps: Optional[float] = None
    survival_time: Optional[float] = None
    additional_metrics: Dict[str, Any] = Field(default_factory=dict)


class SimulationResult(BaseModel):
    """模拟结果"""
    id: str
    simulation_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: SimulationStatus
    data_points: List[DataPoint] = Field(default_factory=list)
    events: List[SimulationEvent] = Field(default_factory=list)
    summary: SimulationSummary = Field(default_factory=SimulationSummary)
    error: Optional[str] = None


class Simulation(BaseModel):
    """模拟定义"""
    id: str
    name: str
    description: Optional[str] = None
    entities: List[Entity] = Field(default_factory=list)
    connections: List[Connection] = Field(default_factory=list)
    settings: SimulationSettings
    results: List[SimulationResult] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    def get_entity_by_id(self, entity_id: str) -> Optional[Entity]:
        """根据ID获取实体"""
        for entity in self.entities:
            if entity.id == entity_id:
                return entity
        return None

    def add_entity(self, entity: Entity) -> None:
        """添加实体"""
        # 检查是否已存在相同ID的实体
        if not self.get_entity_by_id(entity.id):
            self.entities.append(entity)

    def remove_entity(self, entity_id: str) -> bool:
        """移除实体"""
        for i, entity in enumerate(self.entities):
            if entity.id == entity_id:
                del self.entities[i]
                return True
        return False

    class Config:
        """Pydantic配置"""
        use_enum_values = True
