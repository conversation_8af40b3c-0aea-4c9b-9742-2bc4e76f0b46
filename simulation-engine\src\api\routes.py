"""
API路由定义
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Dict, Any
import asyncio
from ..models import Entity, Simulation, SimulationResult
from ..engine import SimulationRunner
from ..utils.sample_data import create_sample_entities

router = APIRouter()

# 临时存储，后续会替换为真正的数据库
_entities: Dict[str, Entity] = {}
_simulations: Dict[str, Simulation] = {}
_results: Dict[str, SimulationResult] = {}

# 全局模拟运行器
_simulation_runner = SimulationRunner()

# 初始化示例数据
def initialize_sample_data():
    """初始化示例数据"""
    sample_entities = create_sample_entities()
    for entity in sample_entities:
        _entities[entity.id] = entity
    print(f"Loaded {len(sample_entities)} sample entities")

# 在模块加载时初始化数据
initialize_sample_data()


@router.get("/entities", response_model=List[Entity])
async def get_entities():
    """获取所有实体"""
    return list(_entities.values())


@router.get("/entities/{entity_id}", response_model=Entity)
async def get_entity(entity_id: str):
    """获取指定实体"""
    if entity_id not in _entities:
        raise HTTPException(status_code=404, detail="Entity not found")
    return _entities[entity_id]


@router.post("/entities", response_model=Entity)
async def create_entity(entity: Entity):
    """创建新实体"""
    if entity.id in _entities:
        raise HTTPException(status_code=400, detail="Entity already exists")
    _entities[entity.id] = entity
    return entity


@router.put("/entities/{entity_id}", response_model=Entity)
async def update_entity(entity_id: str, entity: Entity):
    """更新实体"""
    if entity_id not in _entities:
        raise HTTPException(status_code=404, detail="Entity not found")
    entity.id = entity_id  # 确保ID一致
    _entities[entity_id] = entity
    return entity


@router.delete("/entities/{entity_id}")
async def delete_entity(entity_id: str):
    """删除实体"""
    if entity_id not in _entities:
        raise HTTPException(status_code=404, detail="Entity not found")
    del _entities[entity_id]
    return {"message": "Entity deleted successfully"}


@router.get("/simulations", response_model=List[Simulation])
async def get_simulations():
    """获取所有模拟"""
    return list(_simulations.values())


@router.get("/simulations/{simulation_id}", response_model=Simulation)
async def get_simulation(simulation_id: str):
    """获取指定模拟"""
    if simulation_id not in _simulations:
        raise HTTPException(status_code=404, detail="Simulation not found")
    return _simulations[simulation_id]


@router.post("/simulations", response_model=Simulation)
async def create_simulation(simulation: Simulation):
    """创建新模拟"""
    if simulation.id in _simulations:
        raise HTTPException(status_code=400, detail="Simulation already exists")
    _simulations[simulation.id] = simulation
    return simulation


@router.post("/simulations/{simulation_id}/run")
async def run_simulation(simulation_id: str, background_tasks: BackgroundTasks):
    """运行模拟"""
    if simulation_id not in _simulations:
        raise HTTPException(status_code=404, detail="Simulation not found")

    simulation = _simulations[simulation_id]

    # 在后台运行模拟
    background_tasks.add_task(run_simulation_background, simulation)

    return {"message": f"Simulation {simulation_id} started", "status": "running"}

async def run_simulation_background(simulation: Simulation):
    """在后台运行模拟"""
    try:
        result = await _simulation_runner.run_simulation(simulation)
        _results[result.id] = result
        print(f"Simulation {simulation.id} completed with result {result.id}")
    except Exception as e:
        print(f"Simulation {simulation.id} failed: {e}")


@router.get("/simulations/{simulation_id}/results", response_model=List[SimulationResult])
async def get_simulation_results(simulation_id: str):
    """获取模拟结果"""
    if simulation_id not in _simulations:
        raise HTTPException(status_code=404, detail="Simulation not found")
    
    # 返回该模拟的所有结果
    results = [result for result in _results.values() if result.simulation_id == simulation_id]
    return results


@router.get("/test")
async def test_endpoint():
    """测试端点"""
    return {
        "message": "Simulation engine API is working",
        "entities_count": len(_entities),
        "simulations_count": len(_simulations),
        "results_count": len(_results)
    }
