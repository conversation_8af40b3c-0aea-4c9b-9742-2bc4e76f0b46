import React from 'react';
import { NodeProps } from 'reactflow';
import { Calculate } from '@mui/icons-material';
import BaseNode from './BaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const CalculationNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('calculation');
  
  return (
    <BaseNode
      {...props}
      color={config?.color || '#96ceb4'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<Calculate sx={{ fontSize: 16 }} />}
    />
  );
};

export default CalculationNode;
