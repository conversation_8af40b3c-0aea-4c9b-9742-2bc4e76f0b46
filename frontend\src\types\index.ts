/**
 * 前端类型定义
 * 基于共享类型，添加前端特定的类型
 */

import { EntityType } from '../shared/types';

// 重新导出共享类型
export * from '../shared/types';

// 前端特定的类型
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  entities: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export interface EntityFormData {
  name: string;
  type: EntityType;
  description?: string;
  attributes: Record<string, {
    name: string;
    value: number;
    min?: number;
    max?: number;
    description?: string;
  }>;
  tags: Array<{
    name: string;
    category?: string;
    description?: string;
  }>;
}

export interface SimulationFormData {
  name: string;
  description?: string;
  settings: {
    duration: number;
    tickRate: number;
    randomSeed?: number;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableRealTimeUpdates: boolean;
  };
  selectedEntities: string[];
}

// UI状态类型
export interface UIState {
  loading: boolean;
  error?: string;
  selectedEntity?: string;
  selectedSimulation?: string;
}

// 图表数据类型
export interface ChartDataPoint {
  x: number;
  y: number;
  label?: string;
}

export interface ChartDataset {
  label: string;
  data: ChartDataPoint[];
  borderColor?: string;
  backgroundColor?: string;
  fill?: boolean;
}

export interface ChartData {
  datasets: ChartDataset[];
}
