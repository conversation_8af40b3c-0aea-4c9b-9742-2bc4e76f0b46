import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  AccountTree as LogicIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import { Entity, Simulation, SimulationFormData } from '../types';
import { entityApi, simulationApi } from '../services/api';
import VisualEditor from '../components/VisualEditor';
import SimulationResults from './SimulationResults';

const SimulationEditor: React.FC = () => {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [selectedEntities, setSelectedEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(true);
  const [running, setRunning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  
  const [formData, setFormData] = useState<SimulationFormData>({
    name: 'DPS Test Simulation',
    description: 'Basic DPS testing simulation',
    settings: {
      duration: 60,
      tickRate: 60,
      logLevel: 'info',
      enableRealTimeUpdates: true,
    },
    selectedEntities: [],
  });

  // 加载可用实体
  const loadEntities = async () => {
    try {
      setLoading(true);
      const response = await entityApi.getAll();
      setEntities(response.entities);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load entities');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEntities();
  }, []);

  // 添加实体到模拟
  const handleAddEntity = (entity: Entity) => {
    if (!selectedEntities.find(e => e.id === entity.id)) {
      const newSelected = [...selectedEntities, entity];
      setSelectedEntities(newSelected);
      setFormData({
        ...formData,
        selectedEntities: newSelected.map(e => e.id),
      });
    }
  };

  // 从模拟中移除实体
  const handleRemoveEntity = (entityId: string) => {
    const newSelected = selectedEntities.filter(e => e.id !== entityId);
    setSelectedEntities(newSelected);
    setFormData({
      ...formData,
      selectedEntities: newSelected.map(e => e.id),
    });
  };

  // 运行模拟
  const handleRunSimulation = async () => {
    if (selectedEntities.length === 0) {
      setError('Please select at least one entity for the simulation');
      return;
    }

    try {
      setRunning(true);
      setError(null);
      setSuccess(null);

      // 创建模拟对象
      const simulation: Simulation = {
        id: `sim_${Date.now()}`,
        name: formData.name,
        description: formData.description,
        entities: selectedEntities,
        connections: [], // 暂时为空，后续会实现可视化编辑器
        settings: {
          duration: formData.settings.duration,
          tickRate: formData.settings.tickRate,
          randomSeed: formData.settings.randomSeed,
          logLevel: formData.settings.logLevel,
          enableRealTimeUpdates: formData.settings.enableRealTimeUpdates,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 创建并运行模拟
      await simulationApi.create(simulation);
      const result = await simulationApi.run(simulation.id);
      
      setSuccess(`Simulation started successfully: ${result.message}`);
    } catch (err: any) {
      setError(err.message || 'Failed to run simulation');
    } finally {
      setRunning(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      const result = await simulationApi.testConnection();
      setSuccess(`Connection test successful: ${result.status}`);
      setError(null);
    } catch (err: any) {
      setError(`Connection test failed: ${err.message}`);
      setSuccess(null);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleLogicSave = (nodes: any[], edges: any[]) => {
    console.log('Logic saved:', { nodes, edges });
    setSuccess('逻辑图已保存');
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 紧凑的标签页导航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', flexShrink: 0 }}>
        <Tabs value={tabValue} onChange={handleTabChange} variant="fullWidth">
          <Tab
            icon={<SettingsIcon />}
            label="设置"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
          <Tab
            icon={<LogicIcon />}
            label="逻辑编辑器"
            iconPosition="start"
            sx={{ minHeight: 48 }}
          />
        </Tabs>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* 标签页内容 */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {tabValue === 0 && (
          <Box sx={{ height: '100%', overflow: 'auto', p: 2 }}>
            <Grid container spacing={3}>
            {/* 模拟设置 */}
            <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Simulation Settings
            </Typography>
            
            <TextField
              fullWidth
              label="Simulation Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={2}
            />
            
            <TextField
              fullWidth
              label="Duration (seconds)"
              type="number"
              value={formData.settings.duration}
              onChange={(e) => setFormData({
                ...formData,
                settings: { ...formData.settings, duration: Number(e.target.value) }
              })}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="Tick Rate (per second)"
              type="number"
              value={formData.settings.tickRate}
              onChange={(e) => setFormData({
                ...formData,
                settings: { ...formData.settings, tickRate: Number(e.target.value) }
              })}
              margin="normal"
            />
            
            <FormControl fullWidth margin="normal">
              <InputLabel>Log Level</InputLabel>
              <Select
                value={formData.settings.logLevel}
                onChange={(e) => setFormData({
                  ...formData,
                  settings: { ...formData.settings, logLevel: e.target.value as any }
                })}
                label="Log Level"
              >
                <MenuItem value="debug">Debug</MenuItem>
                <MenuItem value="info">Info</MenuItem>
                <MenuItem value="warn">Warning</MenuItem>
                <MenuItem value="error">Error</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ mt: 2 }}>
              <Button
                variant="contained"
                startIcon={running ? <StopIcon /> : <PlayIcon />}
                onClick={handleRunSimulation}
                disabled={running || selectedEntities.length === 0}
                sx={{ mr: 1 }}
              >
                {running ? 'Running...' : 'Run Simulation'}
              </Button>
              
              <Button
                variant="outlined"
                onClick={handleTestConnection}
              >
                Test Connection
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* 实体选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Available Entities
            </Typography>
            
            <List dense>
              {entities.map((entity) => (
                <ListItem key={entity.id}>
                  <ListItemText
                    primary={entity.name}
                    secondary={
                      <Box>
                        <Chip label={entity.type} size="small" sx={{ mr: 1 }} />
                        {entity.description}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleAddEntity(entity)}
                      disabled={selectedEntities.some(e => e.id === entity.id)}
                    >
                      <AddIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* 选中的实体 */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Selected Entities ({selectedEntities.length})
            </Typography>
            
            {selectedEntities.length === 0 ? (
              <Typography color="text.secondary">
                No entities selected. Add entities from the list above.
              </Typography>
            ) : (
              <List dense>
                {selectedEntities.map((entity) => (
                  <ListItem key={entity.id}>
                    <ListItemText
                      primary={entity.name}
                      secondary={
                        <Box>
                          <Chip label={entity.type} size="small" sx={{ mr: 1 }} />
                          {entity.description}
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleRemoveEntity(entity.id)}
                      >
                        <RemoveIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
          </Box>
        )}

        {/* 可视化逻辑编辑器标签页 */}
        {tabValue === 1 && (
          <Box sx={{
            height: 'calc(100vh - 140px)', // 调整高度计算：AppBar(48px) + Tabs(48px) + margins(44px)
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1
          }}>
            <VisualEditor
              entityId={selectedEntities[0]?.id}
              onSave={handleLogicSave}
              showResults={true}
              resultsComponent={<SimulationResults />}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default SimulationEditor;
