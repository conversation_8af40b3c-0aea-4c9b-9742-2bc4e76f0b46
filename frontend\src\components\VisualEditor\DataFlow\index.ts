import { Node, Edge } from 'reactflow';
import { NodeData, PinDefinition, getNodeTypeConfig } from '../NodeTypes';

// 数据类型定义
export type DataType = 'exec' | 'number' | 'string' | 'boolean' | 'entity' | 'any';

// 连接验证结果
export interface ConnectionValidation {
  isValid: boolean;
  error?: string;
  warning?: string;
}

// 数据流分析结果
export interface DataFlowAnalysis {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  cycles: string[][];
  unreachableNodes: string[];
}

// 检查数据类型兼容性
export const isTypeCompatible = (sourceType: DataType, targetType: DataType): boolean => {
  // 'any' 类型可以连接到任何类型
  if (sourceType === 'any' || targetType === 'any') {
    return true;
  }
  
  // 相同类型直接兼容
  if (sourceType === targetType) {
    return true;
  }
  
  // 数字可以转换为字符串
  if (sourceType === 'number' && targetType === 'string') {
    return true;
  }
  
  // 布尔值可以转换为字符串
  if (sourceType === 'boolean' && targetType === 'string') {
    return true;
  }
  
  return false;
};

// 验证连接是否有效
export const validateConnection = (
  sourceNode: Node<NodeData>,
  sourceHandle: string,
  targetNode: Node<NodeData>,
  targetHandle: string
): ConnectionValidation => {
  // 不能连接到自己
  if (sourceNode.id === targetNode.id) {
    return {
      isValid: false,
      error: '不能连接到自己'
    };
  }

  // 获取节点配置
  const sourceConfig = getNodeTypeConfig(sourceNode.type || '');
  const targetConfig = getNodeTypeConfig(targetNode.type || '');

  if (!sourceConfig || !targetConfig) {
    return {
      isValid: false,
      error: '未知的节点类型'
    };
  }

  // 查找输出引脚
  const sourcePin = sourceConfig.outputs.find(pin => pin.id === sourceHandle);
  if (!sourcePin) {
    return {
      isValid: false,
      error: '源引脚不存在'
    };
  }

  // 查找输入引脚
  const targetPin = targetConfig.inputs.find(pin => pin.id === targetHandle);
  if (!targetPin) {
    return {
      isValid: false,
      error: '目标引脚不存在'
    };
  }

  // 检查类型兼容性
  if (!isTypeCompatible(sourcePin.type as DataType, targetPin.type as DataType)) {
    return {
      isValid: false,
      error: `类型不兼容: ${sourcePin.type} -> ${targetPin.type}`
    };
  }

  // 执行流只能有一个输入连接
  if (targetPin.type === 'exec') {
    // 这里需要检查是否已经有其他连接到这个执行引脚
    // 在实际使用中，需要传入当前的边列表来检查
  }

  return { isValid: true };
};

// 检测循环依赖
export const detectCycles = (nodes: Node<NodeData>[], edges: Edge[]): string[][] => {
  const cycles: string[][] = [];
  const visited = new Set<string>();
  const recursionStack = new Set<string>();

  const dfs = (nodeId: string, path: string[]): void => {
    if (recursionStack.has(nodeId)) {
      // 找到循环
      const cycleStart = path.indexOf(nodeId);
      if (cycleStart !== -1) {
        cycles.push(path.slice(cycleStart));
      }
      return;
    }

    if (visited.has(nodeId)) {
      return;
    }

    visited.add(nodeId);
    recursionStack.add(nodeId);

    // 查找所有从当前节点出发的边
    const outgoingEdges = edges.filter(edge => edge.source === nodeId);
    for (const edge of outgoingEdges) {
      dfs(edge.target, [...path, nodeId]);
    }

    recursionStack.delete(nodeId);
  };

  // 从每个节点开始DFS
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      dfs(node.id, []);
    }
  }

  return cycles;
};

// 查找不可达节点
export const findUnreachableNodes = (nodes: Node<NodeData>[], edges: Edge[]): string[] => {
  const reachable = new Set<string>();
  
  // 找到所有入口节点（没有输入连接的节点）
  const entryNodes = nodes.filter(node => {
    const hasIncomingEdge = edges.some(edge => edge.target === node.id);
    return !hasIncomingEdge;
  });

  // 从入口节点开始BFS
  const queue = [...entryNodes.map(node => node.id)];
  
  while (queue.length > 0) {
    const nodeId = queue.shift()!;
    if (reachable.has(nodeId)) {
      continue;
    }
    
    reachable.add(nodeId);
    
    // 添加所有连接的节点
    const outgoingEdges = edges.filter(edge => edge.source === nodeId);
    for (const edge of outgoingEdges) {
      if (!reachable.has(edge.target)) {
        queue.push(edge.target);
      }
    }
  }

  // 返回不可达的节点
  return nodes
    .filter(node => !reachable.has(node.id))
    .map(node => node.id);
};

// 分析整个数据流图
export const analyzeDataFlow = (nodes: Node<NodeData>[], edges: Edge[]): DataFlowAnalysis => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证所有连接
  for (const edge of edges) {
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);

    if (!sourceNode || !targetNode) {
      errors.push(`连接 ${edge.id} 引用了不存在的节点`);
      continue;
    }

    const validation = validateConnection(
      sourceNode,
      edge.sourceHandle || '',
      targetNode,
      edge.targetHandle || ''
    );

    if (!validation.isValid) {
      errors.push(`连接 ${edge.id}: ${validation.error}`);
    } else if (validation.warning) {
      warnings.push(`连接 ${edge.id}: ${validation.warning}`);
    }
  }

  // 检测循环
  const cycles = detectCycles(nodes, edges);
  if (cycles.length > 0) {
    warnings.push(`检测到 ${cycles.length} 个循环依赖`);
  }

  // 查找不可达节点
  const unreachableNodes = findUnreachableNodes(nodes, edges);
  if (unreachableNodes.length > 0) {
    warnings.push(`发现 ${unreachableNodes.length} 个不可达节点`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    cycles,
    unreachableNodes
  };
};

// 获取节点的输入值
export const getNodeInputValue = (
  nodeId: string,
  inputPin: string,
  nodes: Node<NodeData>[],
  edges: Edge[],
  executionContext: Record<string, any>
): any => {
  // 查找连接到这个输入引脚的边
  const incomingEdge = edges.find(
    edge => edge.target === nodeId && edge.targetHandle === inputPin
  );

  if (!incomingEdge) {
    return undefined;
  }

  // 获取源节点的输出值
  const sourceNode = nodes.find(n => n.id === incomingEdge.source);
  if (!sourceNode) {
    return undefined;
  }

  // 从执行上下文中获取值
  const contextKey = `${incomingEdge.source}.${incomingEdge.sourceHandle}`;
  return executionContext[contextKey];
};

export default {
  isTypeCompatible,
  validateConnection,
  detectCycles,
  findUnreachableNodes,
  analyzeDataFlow,
  getNodeInputValue
};
