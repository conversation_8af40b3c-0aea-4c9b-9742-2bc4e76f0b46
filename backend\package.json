{"name": "game-logic-preview-backend", "version": "0.1.0", "description": "Backend API for Game Logic Preview Tool", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "lint": "eslint src --ext .ts"}, "dependencies": {"express": "^4.18.0", "mongoose": "^7.5.0", "socket.io": "^4.7.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "helmet": "^7.0.0", "morgan": "^1.10.0", "joi": "^17.9.0", "axios": "^1.5.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/morgan": "^1.9.0", "@types/node": "^20.5.0", "@types/jest": "^29.5.0", "typescript": "^5.1.0", "ts-node-dev": "^2.0.0", "jest": "^29.6.0", "ts-jest": "^29.1.0", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "keywords": ["game", "design", "simulation", "api"], "author": "Game Logic Preview Team", "license": "MIT"}