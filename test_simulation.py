#!/usr/bin/env python3
"""
测试脚本 - 验证模拟引擎功能
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'simulation-engine', 'src'))

from models.simulation import Simulation, SimulationSettings
from utils.sample_data import create_sample_entities
from engine.simulation_runner import SimulationRunner


async def test_dps_simulation():
    """测试DPS模拟"""
    print("=== Testing DPS Simulation ===")
    
    # 创建示例实体
    entities = create_sample_entities()
    print(f"Created {len(entities)} sample entities")
    
    # 找到玩家和训练假人
    player = next((e for e in entities if e.id == "player_warrior"), None)
    dummy = next((e for e in entities if e.id == "training_dummy"), None)
    
    if not player or not dummy:
        print("Error: Could not find required entities")
        return
    
    print(f"Player: {player.name} (Attack: {player.get_attribute_value('attack_power')}, Speed: {player.get_attribute_value('attack_speed')})")
    print(f"Target: {dummy.name} (Health: {dummy.get_attribute_value('health')})")
    
    # 创建模拟
    simulation = Simulation(
        id="test_dps_sim",
        name="DPS Test Simulation",
        description="Testing warrior DPS against training dummy",
        entities=[player, dummy],
        connections=[],
        settings=SimulationSettings(
            duration=10.0,  # 10秒测试
            tick_rate=60.0,
            log_level="info",
            enable_real_time_updates=False
        )
    )
    
    # 运行模拟
    runner = SimulationRunner()
    print(f"\nRunning simulation for {simulation.settings.duration} seconds...")
    
    result = await runner.run_simulation(simulation)
    
    # 显示结果
    print(f"\n=== Simulation Results ===")
    print(f"Status: {result.status}")
    print(f"Duration: {result.end_time - result.start_time if result.end_time else 'N/A'}")
    print(f"Total Events: {len(result.events)}")
    print(f"Data Points: {len(result.data_points)}")
    
    if result.summary:
        print(f"\n=== Summary ===")
        if result.summary.total_damage:
            print(f"Total Damage: {result.summary.total_damage:.2f}")
        if result.summary.dps:
            print(f"DPS: {result.summary.dps:.2f}")
        if result.summary.survival_time:
            print(f"Survival Time: {result.summary.survival_time:.2f}s")
    
    # 显示一些事件
    damage_events = [e for e in result.events if e.type == "damage"]
    if damage_events:
        print(f"\n=== Sample Damage Events ===")
        for event in damage_events[:5]:  # 显示前5个伤害事件
            print(f"[{event.timestamp:.2f}s] {event.description}")
    
    print(f"\n=== Test Completed ===")


if __name__ == "__main__":
    asyncio.run(test_dps_simulation())
