/**
 * 简单的后端连接测试脚本
 */

const axios = require('axios');

async function testBackend() {
  console.log('Testing backend connection...');
  
  try {
    // 测试健康检查端点
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:3001/health', {
      timeout: 5000
    });
    console.log('✅ Health check passed:', healthResponse.data);
    
    // 测试实体API端点
    console.log('2. Testing entities endpoint...');
    const entitiesResponse = await axios.get('http://localhost:3001/api/entities', {
      timeout: 5000
    });
    console.log('✅ Entities endpoint passed:', {
      status: entitiesResponse.status,
      dataType: typeof entitiesResponse.data,
      hasEntities: entitiesResponse.data.entities ? entitiesResponse.data.entities.length : 'no entities property'
    });
    
    console.log('\n🎉 All tests passed! Backend is working correctly.');
    
  } catch (error) {
    console.error('❌ Backend test failed:');
    
    if (error.code === 'ECONNREFUSED') {
      console.error('- Backend server is not running on port 3001');
      console.error('- Please start the backend server with: cd backend && npm run dev');
    } else if (error.code === 'ENOTFOUND') {
      console.error('- Cannot resolve localhost');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('- Request timed out - backend might be slow or unresponsive');
    } else {
      console.error('- Error details:', error.message);
      if (error.response) {
        console.error('- Response status:', error.response.status);
        console.error('- Response data:', error.response.data);
      }
    }
  }
}

// 运行测试
testBackend();
