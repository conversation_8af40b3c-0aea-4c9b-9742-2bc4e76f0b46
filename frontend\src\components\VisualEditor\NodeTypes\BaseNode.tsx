import React from 'react';
import { <PERSON>le, Position, NodeProps } from 'reactflow';
import { Box, Typography, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { NodeData, PinDefinition } from './index';

interface BaseNodeProps extends NodeProps<NodeData> {
  color: string;
  inputs?: PinDefinition[];
  outputs?: PinDefinition[];
  icon?: React.ReactNode;
}

const NodeContainer = styled(Paper)<{ nodeColor: string; selected: boolean }>(
  ({ theme, nodeColor, selected }) => ({
    minWidth: 150,
    minHeight: 60,
    border: selected ? `2px solid ${theme.palette.primary.main}` : `1px solid ${nodeColor}`,
    borderRadius: 8,
    backgroundColor: theme.palette.background.paper,
    boxShadow: selected 
      ? `0 0 10px ${theme.palette.primary.main}40`
      : theme.shadows[2],
    position: 'relative',
    overflow: 'visible',
    '&:hover': {
      boxShadow: theme.shadows[4],
    },
  })
);

const NodeHeader = styled(Box)<{ nodeColor: string }>(({ nodeColor }) => ({
  backgroundColor: nodeColor,
  color: 'white',
  padding: '4px 8px',
  borderRadius: '8px 8px 0 0',
  fontSize: '12px',
  fontWeight: 'bold',
  textAlign: 'center',
}));

const NodeBody = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1),
  minHeight: 40,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const PinContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
}));

const PinLabel = styled(Typography)(({ theme }) => ({
  fontSize: '10px',
  color: theme.palette.text.secondary,
  whiteSpace: 'nowrap',
  userSelect: 'none',
}));

// 引脚颜色映射
const getPinColor = (type: string): string => {
  switch (type) {
    case 'exec':
      return '#ffffff';
    case 'number':
      return '#4CAF50';
    case 'string':
      return '#FF9800';
    case 'boolean':
      return '#2196F3';
    case 'entity':
      return '#9C27B0';
    default:
      return '#757575';
  }
};

const BaseNode: React.FC<BaseNodeProps> = ({
  data,
  selected = false,
  color,
  inputs = [],
  outputs = [],
  icon,
}) => {
  return (
    <NodeContainer nodeColor={color} selected={selected}>
      {/* 节点头部 */}
      <NodeHeader nodeColor={color}>
        {data.label}
      </NodeHeader>

      {/* 输入引脚 */}
      {inputs.length > 0 && (
        <PinContainer sx={{ left: -8, top: 30 }}>
          {inputs.map((pin, index) => (
            <Box key={pin.id} sx={{ position: 'relative', height: 16 }}>
              <Handle
                type="target"
                position={Position.Left}
                id={pin.id}
                style={{
                  backgroundColor: getPinColor(pin.type),
                  border: '2px solid #333',
                  width: 12,
                  height: 12,
                  left: -6,
                  top: 2,
                }}
              />
              <PinLabel sx={{ ml: 1, mt: 0.2 }}>
                {pin.label}
              </PinLabel>
            </Box>
          ))}
        </PinContainer>
      )}

      {/* 输出引脚 */}
      {outputs.length > 0 && (
        <PinContainer sx={{ right: -8, top: 30 }}>
          {outputs.map((pin, index) => (
            <Box key={pin.id} sx={{ position: 'relative', height: 16, textAlign: 'right' }}>
              <Handle
                type="source"
                position={Position.Right}
                id={pin.id}
                style={{
                  backgroundColor: getPinColor(pin.type),
                  border: '2px solid #333',
                  width: 12,
                  height: 12,
                  right: -6,
                  top: 2,
                }}
              />
              <PinLabel sx={{ mr: 1, mt: 0.2 }}>
                {pin.label}
              </PinLabel>
            </Box>
          ))}
        </PinContainer>
      )}

      {/* 节点主体 */}
      <NodeBody>
        {icon && (
          <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
            {icon}
          </Box>
        )}
        <Box>
          <Typography variant="body2" sx={{ fontSize: '11px', textAlign: 'center' }}>
            {data.description || data.label}
          </Typography>
        </Box>
      </NodeBody>
    </NodeContainer>
  );
};

export default BaseNode;
