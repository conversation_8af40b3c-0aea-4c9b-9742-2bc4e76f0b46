import React from 'react';
import { NodeProps } from 'reactflow';
import { Storage } from '@mui/icons-material';
import BaseNode from './BaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const VariableNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('variable');
  
  return (
    <BaseNode
      {...props}
      color={config?.color || '#feca57'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<Storage sx={{ fontSize: 16 }} />}
    />
  );
};

export default VariableNode;
