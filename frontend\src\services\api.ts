/**
 * API服务
 * 处理与后端的HTTP通信
 */

import axios, { AxiosResponse } from 'axios';
import { Entity, Simulation, SimulationResult, PaginatedResponse } from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 实体相关API
export const entityApi = {
  // 获取所有实体
  getAll: async (params?: {
    type?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<PaginatedResponse<Entity>> => {
    const response: AxiosResponse<PaginatedResponse<Entity>> = await api.get('/entities', { params });
    return response.data;
  },

  // 根据ID获取实体
  getById: async (id: string): Promise<Entity> => {
    const response: AxiosResponse<Entity> = await api.get(`/entities/${id}`);
    return response.data;
  },

  // 创建实体
  create: async (entity: Omit<Entity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Entity> => {
    const response: AxiosResponse<Entity> = await api.post('/entities', entity);
    return response.data;
  },

  // 更新实体
  update: async (id: string, entity: Partial<Entity>): Promise<Entity> => {
    const response: AxiosResponse<Entity> = await api.put(`/entities/${id}`, entity);
    return response.data;
  },

  // 删除实体
  delete: async (id: string): Promise<void> => {
    await api.delete(`/entities/${id}`);
  },

  // 复制实体
  duplicate: async (id: string): Promise<Entity> => {
    const response: AxiosResponse<Entity> = await api.post(`/entities/${id}/duplicate`);
    return response.data;
  },
};

// 模拟相关API
export const simulationApi = {
  // 获取所有模拟
  getAll: async (): Promise<Simulation[]> => {
    const response: AxiosResponse<Simulation[]> = await api.get('/simulations');
    return response.data;
  },

  // 根据ID获取模拟
  getById: async (id: string): Promise<Simulation> => {
    const response: AxiosResponse<Simulation> = await api.get(`/simulations/${id}`);
    return response.data;
  },

  // 创建模拟
  create: async (simulation: Simulation): Promise<Simulation> => {
    const response: AxiosResponse<Simulation> = await api.post('/simulations', simulation);
    return response.data;
  },

  // 运行模拟
  run: async (id: string): Promise<{ message: string; status: string }> => {
    const response = await api.post(`/simulations/${id}/run`);
    return response.data;
  },

  // 获取模拟结果
  getResults: async (id: string): Promise<SimulationResult[]> => {
    const response: AxiosResponse<SimulationResult[]> = await api.get(`/simulations/${id}/results`);
    return response.data;
  },

  // 测试与模拟引擎的连接
  testConnection: async (): Promise<{ status: string; engine_response: any }> => {
    const response = await api.get('/simulations/test/connection');
    return response.data;
  },
};

// 健康检查API
export const healthApi = {
  check: async (): Promise<{ status: string; service: string; version: string }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export default api;
