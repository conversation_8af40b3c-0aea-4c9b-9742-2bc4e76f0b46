import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Collapse,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Assessment,
  Close,
} from '@mui/icons-material';

interface ResultsPanelProps {
  children: React.ReactNode;
  onClose?: () => void;
  defaultExpanded?: boolean;
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({
  children,
  onClose,
  defaultExpanded = true,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  return (
    <Paper sx={{
      width: 350,
      borderRadius: 0,
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      borderLeft: 1,
      borderColor: 'divider',
      minHeight: 0
    }}>
      {/* 标题栏 */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        p: 1.5,
        borderBottom: 1,
        borderColor: 'divider',
        backgroundColor: 'background.default',
        flexShrink: 0
      }}>
        <Assessment sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="subtitle2" sx={{ flex: 1, fontWeight: 'medium' }}>
          执行结果
        </Typography>
        <Tooltip title={expanded ? "收起" : "展开"}>
          <IconButton
            size="small"
            onClick={() => setExpanded(!expanded)}
            sx={{ mr: 0.5 }}
          >
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Tooltip>
        {onClose && (
          <Tooltip title="关闭">
            <IconButton size="small" onClick={onClose}>
              <Close />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* 内容区域 */}
      <Collapse in={expanded} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ 
          flex: 1, 
          overflow: 'auto',
          minHeight: 0
        }}>
          {children}
        </Box>
      </Collapse>

      {/* 收起状态的提示 */}
      {!expanded && (
        <Box sx={{ 
          p: 1, 
          textAlign: 'center',
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: 'action.hover'
        }}>
          <Typography variant="caption" color="text.secondary">
            点击展开查看结果
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default ResultsPanel;
