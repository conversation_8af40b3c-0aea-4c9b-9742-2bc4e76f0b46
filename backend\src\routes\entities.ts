/**
 * Entity routes
 */

import { Router } from 'express';
import { asyncHandler, createError } from '../utils/errorHandler';
import { logger } from '../utils/logger';
import { Entity, EntityType } from '../../../shared/types';
import { entityService } from '../services/entityService';

const router = Router();

// Initialize sample data
entityService.initializeSampleData();

// Get all entities
router.get('/', asyncHandler(async (req, res) => {
  const { type, search, limit = 50, offset = 0 } = req.query;

  const result = await entityService.getAllEntities({
    type: type as string,
    search: search as string,
    limit: Number(limit),
    offset: Number(offset)
  });

  res.json(result);
}));

// Get entity by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const entity = await entityService.getEntityById(req.params.id);

  if (!entity) {
    throw createError('Entity not found', 404);
  }

  res.json(entity);
}));

// Create new entity
router.post('/', asyncHandler(async (req, res) => {
  const entityData: Omit<Entity, 'id' | 'createdAt' | 'updatedAt'> = req.body;

  // Validate required fields
  if (!entityData.name || !entityData.type) {
    throw createError('Name and type are required', 400);
  }

  if (!Object.values(EntityType).includes(entityData.type)) {
    throw createError('Invalid entity type', 400);
  }

  const entity = await entityService.createEntity(entityData);

  logger.info(`Created entity: ${entity.name} (${entity.id})`);
  res.status(201).json(entity);
}));

// Update entity
router.put('/:id', asyncHandler(async (req, res) => {
  const entityData: Partial<Entity> = req.body;

  const entity = await entityService.updateEntity(req.params.id, entityData);

  if (!entity) {
    throw createError('Entity not found', 404);
  }

  logger.info(`Updated entity: ${entity.name} (${entity.id})`);
  res.json(entity);
}));

// Delete entity
router.delete('/:id', asyncHandler(async (req, res) => {
  const success = await entityService.deleteEntity(req.params.id);

  if (!success) {
    throw createError('Entity not found', 404);
  }

  logger.info(`Deleted entity: ${req.params.id}`);
  res.json({ message: 'Entity deleted successfully' });
}));

// Duplicate entity
router.post('/:id/duplicate', asyncHandler(async (req, res) => {
  const duplicateEntity = await entityService.duplicateEntity(req.params.id);

  if (!duplicateEntity) {
    throw createError('Entity not found', 404);
  }

  logger.info(`Duplicated entity: ${duplicateEntity.name} (${duplicateEntity.id})`);
  res.status(201).json(duplicateEntity);
}));

export default router;
