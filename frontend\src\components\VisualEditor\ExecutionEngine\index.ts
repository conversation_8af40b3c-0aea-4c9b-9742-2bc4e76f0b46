import { Node, Edge } from 'reactflow';
import { NodeData } from '../NodeTypes';

// 执行状态
export interface ExecutionState {
  nodeId: string;
  status: 'idle' | 'executing' | 'completed' | 'error';
  progress: number;
  output?: any;
  error?: string;
  timestamp: number;
}

// 执行上下文
export interface ExecutionContext {
  variables: Record<string, any>;
  entities: Record<string, any>;
  currentTime: number;
  deltaTime: number;
}

// 执行引擎
export class VisualExecutionEngine {
  private nodes: Node<NodeData>[] = [];
  private edges: Edge[] = [];
  private executionStates: Map<string, ExecutionState> = new Map();
  private context: ExecutionContext = {
    variables: {},
    entities: {},
    currentTime: 0,
    deltaTime: 0,
  };
  private isRunning = false;
  private onStateChange?: (states: Map<string, ExecutionState>) => void;

  constructor(onStateChange?: (states: Map<string, ExecutionState>) => void) {
    this.onStateChange = onStateChange;
  }

  // 设置图数据
  setGraph(nodes: Node<NodeData>[], edges: Edge[]) {
    this.nodes = nodes;
    this.edges = edges;
    this.initializeStates();
  }

  // 初始化执行状态
  private initializeStates() {
    this.executionStates.clear();
    for (const node of this.nodes) {
      this.executionStates.set(node.id, {
        nodeId: node.id,
        status: 'idle',
        progress: 0,
        timestamp: Date.now(),
      });
    }
    this.notifyStateChange();
  }

  // 开始执行
  async start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.context.currentTime = 0;
    
    // 找到入口节点（触发器节点）
    const entryNodes = this.nodes.filter(node => node.type === 'trigger');
    
    // 并行执行所有入口节点
    const promises = entryNodes.map(node => this.executeNode(node.id));
    
    try {
      await Promise.all(promises);
    } catch (error) {
      console.error('Execution failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // 停止执行
  stop() {
    this.isRunning = false;
    this.initializeStates();
  }

  // 执行单个节点
  private async executeNode(nodeId: string): Promise<any> {
    if (!this.isRunning) return;

    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return;

    // 更新状态为执行中
    this.updateNodeState(nodeId, {
      status: 'executing',
      progress: 0,
      timestamp: Date.now(),
    });

    try {
      // 模拟执行过程
      const result = await this.simulateNodeExecution(node);
      
      // 更新状态为完成
      this.updateNodeState(nodeId, {
        status: 'completed',
        progress: 100,
        output: result,
        timestamp: Date.now(),
      });

      // 执行连接的下游节点
      await this.executeDownstreamNodes(nodeId);

      return result;
    } catch (error) {
      // 更新状态为错误
      this.updateNodeState(nodeId, {
        status: 'error',
        progress: 0,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
      });
      throw error;
    }
  }

  // 模拟节点执行
  private async simulateNodeExecution(node: Node<NodeData>): Promise<any> {
    const executionTime = this.getNodeExecutionTime(node);
    const steps = 10;
    const stepTime = executionTime / steps;

    for (let i = 0; i < steps; i++) {
      if (!this.isRunning) break;
      
      await new Promise(resolve => setTimeout(resolve, stepTime));
      
      this.updateNodeState(node.id, {
        status: 'executing',
        progress: ((i + 1) / steps) * 100,
        timestamp: Date.now(),
      });
    }

    // 根据节点类型执行不同的逻辑
    return this.executeNodeLogic(node);
  }

  // 获取节点执行时间（毫秒）
  private getNodeExecutionTime(node: Node<NodeData>): number {
    switch (node.type) {
      case 'trigger':
        return 100;
      case 'condition':
        return 200;
      case 'effect':
        return 500;
      case 'calculation':
        return 150;
      default:
        return 300;
    }
  }

  // 执行节点逻辑
  private executeNodeLogic(node: Node<NodeData>): any {
    switch (node.type) {
      case 'trigger':
        return { triggered: true, timestamp: this.context.currentTime };
      
      case 'condition':
        // 简单的条件检查
        const conditionResult = Math.random() > 0.5; // 随机结果用于演示
        return { result: conditionResult };
      
      case 'effect':
        // 效果执行
        const damage = node.data.amount || 100;
        return { damage, applied: true };
      
      case 'calculation':
        // 数值计算
        const a = this.getInputValue(node.id, 'a') || 0;
        const b = this.getInputValue(node.id, 'b') || 0;
        const operation = node.data.operation || 'ADD';
        
        let result = 0;
        switch (operation) {
          case 'ADD':
            result = a + b;
            break;
          case 'SUBTRACT':
            result = a - b;
            break;
          case 'MULTIPLY':
            result = a * b;
            break;
          case 'DIVIDE':
            result = b !== 0 ? a / b : 0;
            break;
          default:
            result = a + b;
        }
        
        return { result };
      
      case 'constant':
        return { value: node.data.value || 0 };
      
      case 'variable':
        const varName = node.data.variableName || 'health';
        const entityId = node.data.entityId || 'self';
        return { value: this.context.variables[`${entityId}.${varName}`] || 0 };
      
      default:
        return { executed: true };
    }
  }

  // 获取输入值
  private getInputValue(nodeId: string, inputPin: string): any {
    const incomingEdge = this.edges.find(
      edge => edge.target === nodeId && edge.targetHandle === inputPin
    );

    if (!incomingEdge) return undefined;

    const sourceState = this.executionStates.get(incomingEdge.source);
    return sourceState?.output;
  }

  // 执行下游节点
  private async executeDownstreamNodes(nodeId: string) {
    const outgoingEdges = this.edges.filter(edge => edge.source === nodeId);
    
    for (const edge of outgoingEdges) {
      // 检查是否是执行流连接
      if (edge.sourceHandle === 'exec' || edge.sourceHandle?.includes('true') || edge.sourceHandle?.includes('false')) {
        await this.executeNode(edge.target);
      }
    }
  }

  // 更新节点状态
  private updateNodeState(nodeId: string, updates: Partial<ExecutionState>) {
    const currentState = this.executionStates.get(nodeId);
    if (currentState) {
      const newState = { ...currentState, ...updates };
      this.executionStates.set(nodeId, newState);
      this.notifyStateChange();
    }
  }

  // 通知状态变化
  private notifyStateChange() {
    if (this.onStateChange) {
      this.onStateChange(new Map(this.executionStates));
    }
  }

  // 获取当前状态
  getExecutionStates(): Map<string, ExecutionState> {
    return new Map(this.executionStates);
  }

  // 检查是否正在运行
  isExecuting(): boolean {
    return this.isRunning;
  }
}

export default VisualExecutionEngine;
