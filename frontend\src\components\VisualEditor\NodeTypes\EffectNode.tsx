import React from 'react';
import { NodeProps } from 'reactflow';
import { FlashOn } from '@mui/icons-material';
import BaseNode from './BaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const EffectNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('effect');
  
  return (
    <BaseNode
      {...props}
      color={config?.color || '#45b7d1'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<FlashOn sx={{ fontSize: 16 }} />}
    />
  );
};

export default EffectNode;
