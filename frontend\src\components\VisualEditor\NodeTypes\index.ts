import { NodeTypes } from 'reactflow';
import TriggerNode from './TriggerNode';
import ConditionNode from './ConditionNode';
import EffectNode from './EffectNode';
import CalculationNode from './CalculationNode';
import VariableNode from './VariableNode';
import ConstantNode from './ConstantNode';

export const nodeTypes: NodeTypes = {
  trigger: TriggerNode,
  condition: ConditionNode,
  effect: EffectNode,
  calculation: CalculationNode,
  variable: VariableNode,
  constant: ConstantNode,
};

// 节点类型定义
export interface NodeData {
  label: string;
  description?: string;
  [key: string]: any;
}

// 引脚类型定义
export interface PinDefinition {
  id: string;
  label: string;
  type: 'exec' | 'number' | 'string' | 'boolean' | 'entity' | 'any';
  required?: boolean;
  description?: string;
}

// 节点类型配置
export interface NodeTypeConfig {
  type: string;
  label: string;
  description: string;
  category: string;
  color: string;
  inputs: PinDefinition[];
  outputs: PinDefinition[];
  defaultData: NodeData;
}

// 预定义的节点类型配置
export const nodeTypeConfigs: NodeTypeConfig[] = [
  {
    type: 'trigger',
    label: '触发器',
    description: '事件触发节点',
    category: 'Events',
    color: '#ff6b6b',
    inputs: [],
    outputs: [
      {
        id: 'exec',
        label: '执行',
        type: 'exec',
        description: '触发执行流'
      }
    ],
    defaultData: {
      label: '触发器',
      triggerType: 'ON_ATTACK'
    }
  },
  {
    type: 'condition',
    label: '条件',
    description: '条件判断节点',
    category: 'Logic',
    color: '#4ecdc4',
    inputs: [
      {
        id: 'exec',
        label: '执行',
        type: 'exec',
        required: true
      }
    ],
    outputs: [
      {
        id: 'true',
        label: '真',
        type: 'exec',
        description: '条件为真时执行'
      },
      {
        id: 'false',
        label: '假',
        type: 'exec',
        description: '条件为假时执行'
      }
    ],
    defaultData: {
      label: '条件',
      conditionType: 'ATTRIBUTE_COMPARE',
      attribute: 'health',
      operator: '>',
      value: 0
    }
  },
  {
    type: 'effect',
    label: '效果',
    description: '执行效果节点',
    category: 'Actions',
    color: '#45b7d1',
    inputs: [
      {
        id: 'exec',
        label: '执行',
        type: 'exec',
        required: true
      },
      {
        id: 'target',
        label: '目标',
        type: 'entity'
      },
      {
        id: 'amount',
        label: '数值',
        type: 'number'
      }
    ],
    outputs: [
      {
        id: 'exec',
        label: '执行',
        type: 'exec'
      }
    ],
    defaultData: {
      label: '效果',
      effectType: 'DAMAGE',
      amount: 100
    }
  },
  {
    type: 'calculation',
    label: '计算',
    description: '数值计算节点',
    category: 'Math',
    color: '#96ceb4',
    inputs: [
      {
        id: 'a',
        label: 'A',
        type: 'number',
        required: true
      },
      {
        id: 'b',
        label: 'B',
        type: 'number',
        required: true
      }
    ],
    outputs: [
      {
        id: 'result',
        label: '结果',
        type: 'number'
      }
    ],
    defaultData: {
      label: '计算',
      operation: 'ADD'
    }
  },
  {
    type: 'variable',
    label: '变量',
    description: '获取变量值',
    category: 'Data',
    color: '#feca57',
    inputs: [],
    outputs: [
      {
        id: 'value',
        label: '值',
        type: 'any'
      }
    ],
    defaultData: {
      label: '变量',
      variableName: 'health',
      entityId: 'self'
    }
  },
  {
    type: 'constant',
    label: '常量',
    description: '常量值',
    category: 'Data',
    color: '#ff9ff3',
    inputs: [],
    outputs: [
      {
        id: 'value',
        label: '值',
        type: 'any'
      }
    ],
    defaultData: {
      label: '常量',
      value: 0,
      valueType: 'number'
    }
  }
];

// 根据类型获取节点配置
export const getNodeTypeConfig = (type: string): NodeTypeConfig | undefined => {
  return nodeTypeConfigs.find(config => config.type === type);
};

// 根据分类获取节点类型
export const getNodeTypesByCategory = (): Record<string, NodeTypeConfig[]> => {
  return nodeTypeConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, NodeTypeConfig[]>);
};
