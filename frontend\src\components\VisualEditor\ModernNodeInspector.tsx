import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider,
  Chip,
  Switch,
  FormControlLabel,
  <PERSON>lider,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Stack,
} from '@mui/material';
import {
  Save,
  PlayArrow,
  Stop,
  ExpandMore,
  ContentCopy,
  Delete,
} from '@mui/icons-material';
import { Node } from 'reactflow';
import { NodeData, getNodeTypeConfig } from './NodeTypes';
import { ExecutionState } from './ExecutionEngine';

interface ModernNodeInspectorProps {
  selectedNode: Node<NodeData> | null;
  onNodeUpdate: (nodeId: string, newData: Partial<NodeData>) => void;
  onSave: () => void;
  onExecute?: () => void;
  onStopExecution?: () => void;
  isExecuting?: boolean;
  executionStates?: Map<string, ExecutionState>;
  onNodeDelete?: (nodeId: string) => void;
  onNodeDuplicate?: (nodeId: string) => void;
}

// 数值输入组件
const NumberInput: React.FC<{
  label: string;
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  showSlider?: boolean;
}> = ({ label, value, onChange, min = 0, max = 100, step = 1, showSlider = false }) => {
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (newValue: number) => {
    setLocalValue(newValue);
    onChange(newValue);
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="caption" color="text.secondary" gutterBottom>
        {label}
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <TextField
          type="number"
          size="small"
          value={localValue}
          onChange={(e) => handleChange(Number(e.target.value))}
          inputProps={{ min, max, step }}
          sx={{ width: showSlider ? 80 : '100%' }}
        />
        {showSlider && (
          <Slider
            value={localValue}
            onChange={(_, newValue) => handleChange(newValue as number)}
            min={min}
            max={max}
            step={step}
            sx={{ flex: 1 }}
          />
        )}
      </Box>
    </Box>
  );
};

const ModernNodeInspector: React.FC<ModernNodeInspectorProps> = ({
  selectedNode,
  onNodeUpdate,
  onSave,
  onExecute,
  onStopExecution,
  isExecuting = false,
  executionStates,
  onNodeDelete,
  onNodeDuplicate,
}) => {
  const [localData, setLocalData] = useState<NodeData | null>(null);

  useEffect(() => {
    if (selectedNode) {
      setLocalData({ ...selectedNode.data });
    } else {
      setLocalData(null);
    }
  }, [selectedNode]);

  const handleDataChange = (key: string, value: any) => {
    if (!localData || !selectedNode) return;

    const newData = { ...localData, [key]: value };
    setLocalData(newData);
    onNodeUpdate(selectedNode.id, { [key]: value });
  };

  const nodeConfig = selectedNode ? getNodeTypeConfig(selectedNode.type || '') : null;
  const executionState = selectedNode ? executionStates?.get(selectedNode.id) : undefined;

  if (!selectedNode || !localData) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          选择一个节点来编辑其属性
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          双击画布可以快速添加节点
        </Typography>
      </Box>
    );
  }

  const renderNodeSpecificFields = () => {
    switch (selectedNode.type) {
      case 'trigger':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>触发类型</InputLabel>
              <Select
                value={localData.triggerType || 'ON_ATTACK'}
                onChange={(e) => handleDataChange('triggerType', e.target.value)}
                label="触发类型"
              >
                <MenuItem value="ON_ATTACK">攻击时</MenuItem>
                <MenuItem value="ON_DAMAGE">受伤时</MenuItem>
                <MenuItem value="ON_HEAL">治疗时</MenuItem>
                <MenuItem value="ON_BUFF_APPLY">获得Buff时</MenuItem>
                <MenuItem value="ON_BUFF_REMOVE">失去Buff时</MenuItem>
                <MenuItem value="ON_DEATH">死亡时</MenuItem>
                <MenuItem value="ON_TURN_START">回合开始</MenuItem>
                <MenuItem value="ON_TURN_END">回合结束</MenuItem>
              </Select>
            </FormControl>
          </Box>
        );

      case 'condition':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>条件类型</InputLabel>
              <Select
                value={localData.conditionType || 'ATTRIBUTE_COMPARE'}
                onChange={(e) => handleDataChange('conditionType', e.target.value)}
                label="条件类型"
              >
                <MenuItem value="ATTRIBUTE_COMPARE">属性比较</MenuItem>
                <MenuItem value="TAG_CHECK">标签检查</MenuItem>
                <MenuItem value="PROBABILITY">概率判断</MenuItem>
                <MenuItem value="CUSTOM">自定义条件</MenuItem>
              </Select>
            </FormControl>

            {localData.conditionType === 'ATTRIBUTE_COMPARE' && (
              <>
                <TextField
                  fullWidth
                  margin="normal"
                  size="small"
                  label="属性名"
                  value={localData.attribute || ''}
                  onChange={(e) => handleDataChange('attribute', e.target.value)}
                />
                <FormControl fullWidth margin="normal" size="small">
                  <InputLabel>比较操作符</InputLabel>
                  <Select
                    value={localData.operator || '>'}
                    onChange={(e) => handleDataChange('operator', e.target.value)}
                    label="比较操作符"
                  >
                    <MenuItem value=">">&gt;</MenuItem>
                    <MenuItem value=">=">&gt;=</MenuItem>
                    <MenuItem value="<">&lt;</MenuItem>
                    <MenuItem value="<=">&lt;=</MenuItem>
                    <MenuItem value="==">=</MenuItem>
                    <MenuItem value="!=">!=</MenuItem>
                  </Select>
                </FormControl>
                <NumberInput
                  label="比较值"
                  value={localData.value || 0}
                  onChange={(value) => handleDataChange('value', value)}
                  min={-1000}
                  max={1000}
                  showSlider={false}
                />
              </>
            )}

            {localData.conditionType === 'PROBABILITY' && (
              <NumberInput
                label="概率 (%)"
                value={localData.probability || 50}
                onChange={(value) => handleDataChange('probability', value)}
                min={0}
                max={100}
                showSlider={true}
              />
            )}
          </Box>
        );

      case 'effect':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>效果类型</InputLabel>
              <Select
                value={localData.effectType || 'DAMAGE'}
                onChange={(e) => handleDataChange('effectType', e.target.value)}
                label="效果类型"
              >
                <MenuItem value="DAMAGE">造成伤害</MenuItem>
                <MenuItem value="HEAL">治疗</MenuItem>
                <MenuItem value="BUFF">施加Buff</MenuItem>
                <MenuItem value="DEBUFF">施加Debuff</MenuItem>
                <MenuItem value="MODIFY_ATTRIBUTE">修改属性</MenuItem>
              </Select>
            </FormControl>

            <NumberInput
              label="数值"
              value={localData.amount || 100}
              onChange={(value) => handleDataChange('amount', value)}
              min={0}
              max={10000}
              showSlider={false}
            />
          </Box>
        );

      case 'calculation':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>运算类型</InputLabel>
              <Select
                value={localData.operation || 'ADD'}
                onChange={(e) => handleDataChange('operation', e.target.value)}
                label="运算类型"
              >
                <MenuItem value="ADD">加法 (+)</MenuItem>
                <MenuItem value="SUBTRACT">减法 (-)</MenuItem>
                <MenuItem value="MULTIPLY">乘法 (×)</MenuItem>
                <MenuItem value="DIVIDE">除法 (÷)</MenuItem>
                <MenuItem value="POWER">幂运算 (^)</MenuItem>
                <MenuItem value="MIN">最小值</MenuItem>
                <MenuItem value="MAX">最大值</MenuItem>
              </Select>
            </FormControl>
          </Box>
        );

      case 'variable':
        return (
          <Box>
            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="变量名"
              value={localData.variableName || ''}
              onChange={(e) => handleDataChange('variableName', e.target.value)}
            />
            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="实体ID"
              value={localData.entityId || 'self'}
              onChange={(e) => handleDataChange('entityId', e.target.value)}
              helperText="使用 'self' 表示当前实体"
            />
          </Box>
        );

      case 'constant':
        return (
          <Box>
            <FormControl fullWidth margin="normal" size="small">
              <InputLabel>数据类型</InputLabel>
              <Select
                value={localData.valueType || 'number'}
                onChange={(e) => handleDataChange('valueType', e.target.value)}
                label="数据类型"
              >
                <MenuItem value="number">数字</MenuItem>
                <MenuItem value="string">字符串</MenuItem>
                <MenuItem value="boolean">布尔值</MenuItem>
              </Select>
            </FormControl>

            {localData.valueType === 'number' && (
              <NumberInput
                label="数值"
                value={localData.value || 0}
                onChange={(value) => handleDataChange('value', value)}
                min={-10000}
                max={10000}
                showSlider={false}
              />
            )}

            {localData.valueType === 'string' && (
              <TextField
                fullWidth
                margin="normal"
                size="small"
                label="字符串值"
                value={localData.value || ''}
                onChange={(e) => handleDataChange('value', e.target.value)}
              />
            )}

            {localData.valueType === 'boolean' && (
              <FormControlLabel
                control={
                  <Switch
                    checked={localData.value || false}
                    onChange={(e) => handleDataChange('value', e.target.checked)}
                  />
                }
                label="布尔值"
                sx={{ mt: 1 }}
              />
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 节点信息卡片 */}
      <Card sx={{ mb: 2 }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip
                label={selectedNode.type}
                size="small"
                sx={{ backgroundColor: nodeConfig?.color, color: 'white' }}
              />
              <Typography variant="subtitle2">
                {nodeConfig?.label || selectedNode.type}
              </Typography>
            </Box>
          }
          action={
            <Box>
              <Tooltip title="复制节点">
                <IconButton size="small" onClick={() => onNodeDuplicate?.(selectedNode.id)}>
                  <ContentCopy sx={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
              <Tooltip title="删除节点">
                <IconButton size="small" onClick={() => onNodeDelete?.(selectedNode.id)}>
                  <Delete sx={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
            </Box>
          }
          sx={{ pb: 1 }}
        />
        <CardContent sx={{ pt: 0 }}>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            ID: {selectedNode.id}
          </Typography>
          {nodeConfig?.description && (
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: 11 }}>
              {nodeConfig.description}
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* 执行状态 */}
      {executionState && (
        <Alert
          severity={executionState.status === 'error' ? 'error' : 'info'}
          sx={{ mb: 2, fontSize: 11 }}
        >
          状态: {executionState.status}
          {executionState.timestamp && (
            <Typography variant="caption" display="block">
              最后执行: {new Date(executionState.timestamp).toLocaleTimeString()}
            </Typography>
          )}
        </Alert>
      )}

      {/* 基本属性 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle2">基本属性</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="节点名称"
              value={localData?.label || ''}
              onChange={(e) => handleDataChange('label', e.target.value)}
            />

            <TextField
              fullWidth
              margin="normal"
              size="small"
              label="描述"
              multiline
              rows={2}
              value={localData?.description || ''}
              onChange={(e) => handleDataChange('description', e.target.value)}
            />
          </AccordionDetails>
        </Accordion>

        {/* 节点特定属性 */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle2">节点配置</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {renderNodeSpecificFields()}
          </AccordionDetails>
        </Accordion>

        {/* 高级设置 */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle2">高级设置</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <FormControlLabel
              control={
                <Switch
                  checked={localData?.enabled !== false}
                  onChange={(e) => handleDataChange('enabled', e.target.checked)}
                />
              }
              label="启用节点"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={localData?.debugMode || false}
                  onChange={(e) => handleDataChange('debugMode', e.target.checked)}
                />
              }
              label="调试模式"
            />
          </AccordionDetails>
        </Accordion>
      </Box>

      {/* 操作按钮 */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Stack spacing={1}>
          <Button
            fullWidth
            variant="contained"
            startIcon={<Save />}
            onClick={onSave}
            size="small"
          >
            保存
          </Button>
          {onExecute && (
            <Button
              fullWidth
              variant="outlined"
              startIcon={isExecuting ? <Stop /> : <PlayArrow />}
              onClick={isExecuting ? onStopExecution : onExecute}
              color={isExecuting ? "error" : "primary"}
              size="small"
            >
              {isExecuting ? '停止执行' : '开始执行'}
            </Button>
          )}
        </Stack>
      </Box>
    </Box>
  );
};

export default ModernNodeInspector;
