"""
事件系统
处理游戏中的各种事件和事件监听
"""

from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
import uuid
from datetime import datetime


class EventType(str, Enum):
    """事件类型枚举"""
    ATTACK = "attack"
    DAMAGE = "damage"
    HEAL = "heal"
    BUFF_APPLY = "buff_apply"
    BUFF_REMOVE = "buff_remove"
    DEATH = "death"
    TURN_START = "turn_start"
    TURN_END = "turn_end"
    COOLDOWN_READY = "cooldown_ready"
    ATTRIBUTE_CHANGE = "attribute_change"
    CUSTOM = "custom"


@dataclass
class Event:
    """事件数据结构"""
    id: str
    type: EventType
    timestamp: float
    source_id: Optional[str] = None
    target_id: Optional[str] = None
    data: Dict[str, Any] = None
    description: str = ""

    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if not self.id:
            self.id = str(uuid.uuid4())


EventHandler = Callable[[Event], None]


class EventSystem:
    """事件系统管理器"""

    def __init__(self):
        self._handlers: Dict[EventType, List[EventHandler]] = {}
        self._event_history: List[Event] = []
        self._current_time: float = 0.0

    def subscribe(self, event_type: EventType, handler: EventHandler) -> None:
        """订阅事件"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)

    def unsubscribe(self, event_type: EventType, handler: EventHandler) -> None:
        """取消订阅事件"""
        if event_type in self._handlers:
            try:
                self._handlers[event_type].remove(handler)
            except ValueError:
                pass  # Handler not found

    def emit(self, event: Event) -> None:
        """发出事件"""
        event.timestamp = self._current_time
        self._event_history.append(event)

        # 触发所有订阅的处理器
        if event.type in self._handlers:
            for handler in self._handlers[event.type]:
                try:
                    handler(event)
                except Exception as e:
                    print(f"Error in event handler: {e}")

    def create_and_emit(
        self,
        event_type: EventType,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        description: str = ""
    ) -> Event:
        """创建并发出事件"""
        event = Event(
            id=str(uuid.uuid4()),
            type=event_type,
            timestamp=self._current_time,
            source_id=source_id,
            target_id=target_id,
            data=data or {},
            description=description
        )
        self.emit(event)
        return event

    def set_current_time(self, time: float) -> None:
        """设置当前时间"""
        self._current_time = time

    def get_events(
        self,
        event_type: Optional[EventType] = None,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None
    ) -> List[Event]:
        """获取事件历史"""
        events = self._event_history

        # 过滤条件
        if event_type:
            events = [e for e in events if e.type == event_type]
        if source_id:
            events = [e for e in events if e.source_id == source_id]
        if target_id:
            events = [e for e in events if e.target_id == target_id]
        if start_time is not None:
            events = [e for e in events if e.timestamp >= start_time]
        if end_time is not None:
            events = [e for e in events if e.timestamp <= end_time]

        return events

    def clear_history(self) -> None:
        """清空事件历史"""
        self._event_history.clear()

    def get_event_count(self) -> int:
        """获取事件总数"""
        return len(self._event_history)
