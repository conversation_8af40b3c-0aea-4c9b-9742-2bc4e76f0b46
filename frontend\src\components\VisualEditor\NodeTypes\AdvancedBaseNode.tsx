import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Chip,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  ExpandLess,
  Settings,
  PlayArrow,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { NodeData, PinDefinition } from './index';

interface AdvancedBaseNodeProps extends NodeProps<NodeData> {
  color: string;
  inputs?: PinDefinition[];
  outputs?: PinDefinition[];
  icon?: React.ReactNode;
  onDataChange?: (nodeId: string, data: Partial<NodeData>) => void;
  isExecuting?: boolean;
  executionProgress?: number;
}

const NodeContainer = styled(Paper)<{ 
  nodeColor: string; 
  selected: boolean; 
  isExecuting: boolean;
}>(({ theme, nodeColor, selected, isExecuting }) => ({
  minWidth: 180,
  minHeight: 80,
  border: selected 
    ? `2px solid ${theme.palette.primary.main}` 
    : isExecuting
    ? `2px solid ${theme.palette.success.main}`
    : `1px solid ${nodeColor}`,
  borderRadius: 12,
  backgroundColor: theme.palette.background.paper,
  boxShadow: selected 
    ? `0 0 15px ${theme.palette.primary.main}40`
    : isExecuting
    ? `0 0 15px ${theme.palette.success.main}40`
    : theme.shadows[3],
  position: 'relative',
  overflow: 'visible',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    boxShadow: theme.shadows[6],
    transform: 'translateY(-1px)',
  },
}));

const NodeHeader = styled(Box)<{ nodeColor: string; isExecuting: boolean }>(
  ({ theme, nodeColor, isExecuting }) => ({
    background: isExecuting 
      ? `linear-gradient(45deg, ${nodeColor}, ${theme.palette.success.main})`
      : nodeColor,
    color: 'white',
    padding: '6px 12px',
    borderRadius: '12px 12px 0 0',
    fontSize: '13px',
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    overflow: 'hidden',
  })
);

const ExecutionProgress = styled(Box)<{ progress: number }>(({ progress }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  height: '3px',
  width: `${progress}%`,
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  transition: 'width 0.3s ease-in-out',
}));

const NodeBody = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5),
  minHeight: 50,
}));

const PinContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.8),
  zIndex: 10,
}));

const PinWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  height: 20,
  display: 'flex',
  alignItems: 'center',
}));

const PinLabel = styled(Typography)<{ isInput: boolean }>(({ theme, isInput }) => ({
  fontSize: '11px',
  color: theme.palette.text.primary,
  fontWeight: 500,
  whiteSpace: 'nowrap',
  userSelect: 'none',
  backgroundColor: theme.palette.background.paper,
  padding: '2px 6px',
  borderRadius: 4,
  border: `1px solid ${theme.palette.divider}`,
  marginLeft: isInput ? 8 : 0,
  marginRight: isInput ? 0 : 8,
}));

const getPinColor = (type: string): string => {
  switch (type) {
    case 'exec':
      return '#ffffff';
    case 'number':
      return '#4CAF50';
    case 'string':
      return '#FF9800';
    case 'boolean':
      return '#2196F3';
    case 'entity':
      return '#9C27B0';
    default:
      return '#757575';
  }
};

const AdvancedBaseNode: React.FC<AdvancedBaseNodeProps> = ({
  id,
  data,
  selected = false,
  color,
  inputs = [],
  outputs = [],
  icon,
  onDataChange,
  isExecuting = false,
  executionProgress = 0,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleDataUpdate = useCallback((key: string, value: any) => {
    const newData = { ...localData, [key]: value };
    setLocalData(newData);
    if (onDataChange) {
      onDataChange(id, { [key]: value });
    }
  }, [id, localData, onDataChange]);

  const renderInlineEditor = () => {
    if (!expanded) return null;

    return (
      <Collapse in={expanded}>
        <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
          {/* 根据节点类型渲染不同的编辑器 */}
          {data.label && (
            <TextField
              size="small"
              fullWidth
              label="名称"
              value={localData.label || ''}
              onChange={(e) => handleDataUpdate('label', e.target.value)}
              sx={{ mb: 1 }}
            />
          )}
          
          {/* 节点特定的快速编辑字段 */}
          {renderQuickEditFields()}
        </Box>
      </Collapse>
    );
  };

  const renderQuickEditFields = () => {
    // 这里可以根据节点类型渲染不同的快速编辑字段
    switch (data.nodeType || 'default') {
      case 'constant':
        return (
          <TextField
            size="small"
            fullWidth
            label="值"
            type="number"
            value={localData.value || 0}
            onChange={(e) => handleDataUpdate('value', Number(e.target.value))}
          />
        );
      case 'condition':
        return (
          <FormControl size="small" fullWidth>
            <Select
              value={localData.operator || '>'}
              onChange={(e) => handleDataUpdate('operator', e.target.value)}
            >
              <MenuItem value=">">&gt;</MenuItem>
              <MenuItem value=">=">&gt;=</MenuItem>
              <MenuItem value="<">&lt;</MenuItem>
              <MenuItem value="<=">&lt;=</MenuItem>
              <MenuItem value="==">=</MenuItem>
              <MenuItem value="!=">!=</MenuItem>
            </Select>
          </FormControl>
        );
      default:
        return null;
    }
  };

  return (
    <NodeContainer 
      nodeColor={color} 
      selected={selected} 
      isExecuting={isExecuting}
      elevation={selected ? 8 : 3}
    >
      {/* 节点头部 */}
      <NodeHeader nodeColor={color} isExecuting={isExecuting}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {icon}
          <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: 13 }}>
            {data.label}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {isExecuting && (
            <Tooltip title="正在执行">
              <PlayArrow sx={{ fontSize: 16, animation: 'pulse 1s infinite' }} />
            </Tooltip>
          )}
          
          <Tooltip title={expanded ? "收起" : "展开编辑"}>
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              sx={{ color: 'white', p: 0.5 }}
            >
              {expanded ? <ExpandLess /> : <Settings />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* 执行进度条 */}
        {isExecuting && (
          <ExecutionProgress progress={executionProgress} />
        )}
      </NodeHeader>

      {/* 输入引脚 */}
      {inputs.length > 0 && (
        <PinContainer sx={{ left: -10, top: 45 }}>
          {inputs.map((pin, index) => (
            <PinWrapper key={pin.id}>
              <Handle
                type="target"
                position={Position.Left}
                id={pin.id}
                style={{
                  backgroundColor: getPinColor(pin.type),
                  border: '2px solid #333',
                  width: 14,
                  height: 14,
                  left: -7,
                }}
              />
              <PinLabel isInput={true}>
                {pin.label}
              </PinLabel>
            </PinWrapper>
          ))}
        </PinContainer>
      )}

      {/* 输出引脚 */}
      {outputs.length > 0 && (
        <PinContainer sx={{ right: -10, top: 45 }}>
          {outputs.map((pin, index) => (
            <PinWrapper key={pin.id} sx={{ justifyContent: 'flex-end' }}>
              <PinLabel isInput={false}>
                {pin.label}
              </PinLabel>
              <Handle
                type="source"
                position={Position.Right}
                id={pin.id}
                style={{
                  backgroundColor: getPinColor(pin.type),
                  border: '2px solid #333',
                  width: 14,
                  height: 14,
                  right: -7,
                }}
              />
            </PinWrapper>
          ))}
        </PinContainer>
      )}

      {/* 节点主体 */}
      <NodeBody>
        <Typography 
          variant="body2" 
          sx={{ 
            fontSize: 12, 
            textAlign: 'center',
            color: 'text.secondary',
            mb: 1
          }}
        >
          {data.description || ''}
        </Typography>

        {/* 状态指示器 */}
        {data.tags && data.tags.length > 0 && (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
            {data.tags.slice(0, 2).map((tag: string, index: number) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                variant="outlined"
                sx={{ fontSize: 9, height: 18 }}
              />
            ))}
          </Box>
        )}
      </NodeBody>

      {/* 内联编辑器 */}
      {renderInlineEditor()}
    </NodeContainer>
  );
};

export default AdvancedBaseNode;
