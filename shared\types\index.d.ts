export type EntityId = string;
export type NodeId = string;
export type SimulationId = string;
export declare enum EntityType {
    CHARACTER = "CHARACTER",
    SKILL = "SKILL",
    EQUIPMENT = "EQUIPMENT",
    BUFF = "BUFF",
    MONSTER = "MONSTER",
    ITEM = "ITEM",
    CONTAINER = "CONTAINER",
    GRID = "GRID"
}
export declare enum NodeType {
    TRIGGER = "TRIGGER",
    CONDITION = "CONDITION",
    EFFECT = "EFFECT",
    CALCULATION = "CALCULATION",
    PROBABILITY = "PROBABILITY",
    DELAY = "DELAY",
    VARIABLE = "VARIABLE",
    CONSTANT = "CONSTANT"
}
export declare enum TriggerType {
    ON_ATTACK = "ON_ATTACK",
    ON_DAMAGE = "ON_DAMAGE",
    ON_HEAL = "ON_HEAL",
    ON_BUFF_APPLY = "ON_BUFF_APPLY",
    ON_BUFF_REMOVE = "ON_BUFF_REMOVE",
    ON_DEATH = "ON_DEATH",
    ON_TURN_START = "ON_TURN_START",
    ON_TURN_END = "ON_TURN_END",
    ON_COOLDOWN_READY = "ON_COOLDOWN_READY"
}
export interface Attribute {
    name: string;
    value: number;
    min?: number;
    max?: number;
    description?: string;
}
export interface Tag {
    name: string;
    category?: string;
    description?: string;
}
export interface Connection {
    id: string;
    from: {
        nodeId: NodeId;
        outputPin: string;
    };
    to: {
        nodeId: NodeId;
        inputPin: string;
    };
}
export interface LogicNode {
    id: NodeId;
    type: NodeType;
    name: string;
    position: {
        x: number;
        y: number;
    };
    inputs: {
        [pinName: string]: {
            type: string;
            required: boolean;
            description?: string;
        };
    };
    outputs: {
        [pinName: string]: {
            type: string;
            description?: string;
        };
    };
    parameters: Record<string, any>;
    description?: string;
}
export interface Entity {
    id: EntityId;
    name: string;
    type: EntityType;
    description?: string;
    attributes: Record<string, Attribute>;
    tags: Tag[];
    logic: LogicNode[];
    connections: Connection[];
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
export interface SimulationSettings {
    duration: number;
    tickRate: number;
    randomSeed?: number;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableRealTimeUpdates: boolean;
}
export interface DataPoint {
    timestamp: number;
    entityId: EntityId;
    attribute: string;
    value: number;
}
export interface SimulationEvent {
    id: string;
    timestamp: number;
    type: string;
    sourceId?: EntityId;
    targetId?: EntityId;
    data: Record<string, any>;
    description: string;
}
export interface SimulationResult {
    id: string;
    simulationId: SimulationId;
    startTime: Date;
    endTime?: Date;
    status: 'running' | 'completed' | 'failed' | 'cancelled';
    dataPoints: DataPoint[];
    events: SimulationEvent[];
    summary: {
        totalDamage?: number;
        dps?: number;
        totalHealing?: number;
        hps?: number;
        survivalTime?: number;
        [key: string]: any;
    };
    error?: string;
}
export interface Simulation {
    id: SimulationId;
    name: string;
    description?: string;
    entities: Entity[];
    connections: Connection[];
    settings: SimulationSettings;
    results?: SimulationResult[];
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=index.d.ts.map