import React from 'react';
import {
  EdgeProps,
  getSmoothStepPath,
  EdgeLabelRenderer,
  BaseEdge,
} from 'reactflow';
import { Box, Typography } from '@mui/material';

const DataFlowEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  selected,
}) => {
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          strokeWidth: selected ? 3 : 2,
          stroke: selected ? '#1976d2' : '#b1b1b7',
        }}
      />
      {data?.label && (
        <EdgeLabelRenderer>
          <Box
            sx={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 10,
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              padding: '2px 4px',
              borderRadius: 1,
              border: '1px solid #ccc',
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            <Typography variant="caption" sx={{ fontSize: 10 }}>
              {data.label}
            </Typography>
          </Box>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

export default DataFlowEdge;
