"""
状态管理器
管理游戏中所有实体的状态和属性
"""

from typing import Dict, List, Optional, Any
from ..models.entity import Entity, Attribute
from .event_system import EventSystem, EventType


class StateManager:
    """状态管理器"""

    def __init__(self, event_system: EventSystem):
        self.event_system = event_system
        self._entities: Dict[str, Entity] = {}
        self._entity_states: Dict[str, Dict[str, Any]] = {}

    def add_entity(self, entity: Entity) -> None:
        """添加实体"""
        self._entities[entity.id] = entity
        self._entity_states[entity.id] = {}

    def remove_entity(self, entity_id: str) -> None:
        """移除实体"""
        if entity_id in self._entities:
            del self._entities[entity_id]
        if entity_id in self._entity_states:
            del self._entity_states[entity_id]

    def get_entity(self, entity_id: str) -> Optional[Entity]:
        """获取实体"""
        return self._entities.get(entity_id)

    def get_all_entities(self) -> List[Entity]:
        """获取所有实体"""
        return list(self._entities.values())

    def get_entities_by_type(self, entity_type: str) -> List[Entity]:
        """根据类型获取实体"""
        return [entity for entity in self._entities.values() if entity.type == entity_type]

    def get_entities_with_tag(self, tag_name: str) -> List[Entity]:
        """获取具有指定标签的实体"""
        return [entity for entity in self._entities.values() if entity.has_tag(tag_name)]

    def get_attribute_value(self, entity_id: str, attribute_name: str) -> float:
        """获取实体属性值"""
        entity = self.get_entity(entity_id)
        if entity:
            return entity.get_attribute_value(attribute_name)
        return 0.0

    def set_attribute_value(self, entity_id: str, attribute_name: str, value: float) -> None:
        """设置实体属性值"""
        entity = self.get_entity(entity_id)
        if entity:
            old_value = entity.get_attribute_value(attribute_name)
            entity.set_attribute_value(attribute_name, value)
            
            # 发出属性变化事件
            self.event_system.create_and_emit(
                EventType.ATTRIBUTE_CHANGE,
                source_id=entity_id,
                data={
                    "attribute": attribute_name,
                    "old_value": old_value,
                    "new_value": value,
                    "change": value - old_value
                },
                description=f"{entity.name} {attribute_name}: {old_value} -> {value}"
            )

    def modify_attribute_value(self, entity_id: str, attribute_name: str, delta: float) -> None:
        """修改实体属性值（增加或减少）"""
        current_value = self.get_attribute_value(entity_id, attribute_name)
        self.set_attribute_value(entity_id, attribute_name, current_value + delta)

    def get_state(self, entity_id: str, key: str, default: Any = None) -> Any:
        """获取实体状态"""
        if entity_id in self._entity_states:
            return self._entity_states[entity_id].get(key, default)
        return default

    def set_state(self, entity_id: str, key: str, value: Any) -> None:
        """设置实体状态"""
        if entity_id not in self._entity_states:
            self._entity_states[entity_id] = {}
        self._entity_states[entity_id][key] = value

    def has_state(self, entity_id: str, key: str) -> bool:
        """检查实体是否有指定状态"""
        if entity_id in self._entity_states:
            return key in self._entity_states[entity_id]
        return False

    def clear_state(self, entity_id: str, key: str) -> None:
        """清除实体状态"""
        if entity_id in self._entity_states and key in self._entity_states[entity_id]:
            del self._entity_states[entity_id][key]

    def get_all_states(self, entity_id: str) -> Dict[str, Any]:
        """获取实体的所有状态"""
        return self._entity_states.get(entity_id, {}).copy()

    def clear_all_states(self, entity_id: str) -> None:
        """清除实体的所有状态"""
        if entity_id in self._entity_states:
            self._entity_states[entity_id].clear()

    def is_alive(self, entity_id: str) -> bool:
        """检查实体是否存活（生命值大于0）"""
        hp = self.get_attribute_value(entity_id, "health")
        max_hp = self.get_attribute_value(entity_id, "max_health")
        
        # 如果没有生命值属性，认为是存活的
        if max_hp <= 0:
            return True
            
        return hp > 0

    def kill_entity(self, entity_id: str) -> None:
        """杀死实体"""
        entity = self.get_entity(entity_id)
        if entity and self.is_alive(entity_id):
            self.set_attribute_value(entity_id, "health", 0)
            
            # 发出死亡事件
            self.event_system.create_and_emit(
                EventType.DEATH,
                source_id=entity_id,
                description=f"{entity.name} has died"
            )
