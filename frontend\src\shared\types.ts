/**
 * 共享类型定义
 * 用于前端、后端和模拟引擎之间的数据交换
 */

// 基础类型
export type EntityId = string;
export type NodeId = string;
export type SimulationId = string;

// 实体类型枚举
export enum EntityType {
  CHARACTER = 'CHARACTER',
  SKILL = 'SKILL',
  EQUIPMENT = 'EQUIPMENT',
  BUFF = 'BUFF',
  MONSTER = 'MONSTER',
  ITEM = 'ITEM',
  CONTAINER = 'CONTAINER',
  GRID = 'GRID'
}

// 逻辑节点类型枚举
export enum NodeType {
  TRIGGER = 'TRIGGER',
  CONDITION = 'CONDITION',
  EFFECT = 'EFFECT',
  CALCULATION = 'CALCULATION',
  PROBABILITY = 'PROBABILITY',
  DELAY = 'DELAY',
  VARIABLE = 'VARIABLE',
  CONSTANT = 'CONSTANT'
}

// 触发器类型
export enum TriggerType {
  ON_ATTACK = 'ON_ATTACK',
  ON_DAMAGE = 'ON_DAMAGE',
  ON_HEAL = 'ON_HEAL',
  ON_BUFF_APPLY = 'ON_BUFF_APPLY',
  ON_BUFF_REMOVE = 'ON_BUFF_REMOVE',
  ON_DEATH = 'ON_DEATH',
  ON_TURN_START = 'ON_TURN_START',
  ON_TURN_END = 'ON_TURN_END',
  ON_COOLDOWN_READY = 'ON_COOLDOWN_READY'
}

// 属性定义
export interface Attribute {
  name: string;
  value: number;
  min?: number;
  max?: number;
  description?: string;
}

// 标签定义
export interface Tag {
  name: string;
  category?: string;
  description?: string;
}

// 连接定义
export interface Connection {
  id: string;
  from: {
    nodeId: NodeId;
    outputPin: string;
  };
  to: {
    nodeId: NodeId;
    inputPin: string;
  };
}

// 逻辑节点定义
export interface LogicNode {
  id: NodeId;
  type: NodeType;
  name: string;
  position: {
    x: number;
    y: number;
  };
  inputs: {
    [pinName: string]: {
      type: string;
      required: boolean;
      description?: string;
    };
  };
  outputs: {
    [pinName: string]: {
      type: string;
      description?: string;
    };
  };
  parameters: Record<string, any>;
  description?: string;
}

// 实体定义
export interface Entity {
  id: EntityId;
  name: string;
  type: EntityType;
  description?: string;
  attributes: Record<string, Attribute>;
  tags: Tag[];
  logic: LogicNode[];
  connections: Connection[];
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// 模拟设置
export interface SimulationSettings {
  duration: number; // 模拟持续时间（秒）
  tickRate: number; // 每秒tick数
  randomSeed?: number; // 随机种子
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableRealTimeUpdates: boolean;
}

// 模拟结果数据点
export interface DataPoint {
  timestamp: number;
  entityId: EntityId;
  attribute: string;
  value: number;
}

// 模拟事件
export interface SimulationEvent {
  id: string;
  timestamp: number;
  type: string;
  sourceId?: EntityId;
  targetId?: EntityId;
  data: Record<string, any>;
  description: string;
}

// 模拟结果
export interface SimulationResult {
  id: string;
  simulationId: SimulationId;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  dataPoints: DataPoint[];
  events: SimulationEvent[];
  summary: {
    totalDamage?: number;
    dps?: number;
    totalHealing?: number;
    hps?: number;
    survivalTime?: number;
    [key: string]: any;
  };
  error?: string;
}

// 模拟定义
export interface Simulation {
  id: SimulationId;
  name: string;
  description?: string;
  entities: Entity[];
  connections: Connection[];
  settings: SimulationSettings;
  results?: SimulationResult[];
  createdAt: Date;
  updatedAt: Date;
}
