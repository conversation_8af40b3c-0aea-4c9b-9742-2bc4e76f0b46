import React from 'react';
import { NodeProps } from 'reactflow';
import { Looks } from '@mui/icons-material';
import BaseNode from './BaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const ConstantNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('constant');
  
  return (
    <BaseNode
      {...props}
      color={config?.color || '#ff9ff3'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<Looks sx={{ fontSize: 16 }} />}
    />
  );
};

export default ConstantNode;
