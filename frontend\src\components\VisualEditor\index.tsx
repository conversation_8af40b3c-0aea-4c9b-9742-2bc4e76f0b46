import React, { useCallback, useState, useRef, useEffect } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
  BackgroundVariant,
} from 'reactflow';
import { Box, Paper, Typography, Alert, Collapse } from '@mui/material';
import 'reactflow/dist/style.css';

// 导入自定义节点类型
import { nodeTypes } from './NodeTypes';
import { edgeTypes } from './EdgeTypes';
import NodePalette from './NodePalette';
import NodeInspector from './NodeInspector';
import ModernNodePalette from './ModernNodePalette';
import ModernNodeInspector from './ModernNodeInspector';
import NodeSearch from './NodeSearch';
import EditorToolbar from './EditorToolbar';
import ResultsPanel from './ResultsPanel';
import ResizableSidePanel from './ResizableSidePanel';
import { validateConnection, analyzeDataFlow } from './DataFlow';
import { VisualExecutionEngine, ExecutionState } from './ExecutionEngine';

// 初始节点数据
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'trigger',
    position: { x: 100, y: 100 },
    data: {
      label: 'On Attack',
      triggerType: 'ON_ATTACK',
      description: '当攻击时触发'
    },
  },
  {
    id: '2',
    type: 'condition',
    position: { x: 300, y: 100 },
    data: {
      label: 'Check Health',
      conditionType: 'ATTRIBUTE_COMPARE',
      attribute: 'health',
      operator: '>',
      value: 50,
      description: '检查生命值是否大于50'
    },
  },
  {
    id: '3',
    type: 'effect',
    position: { x: 500, y: 100 },
    data: {
      label: 'Deal Damage',
      effectType: 'DAMAGE',
      amount: 100,
      description: '造成100点伤害'
    },
  },
];

// 初始连接线数据
const initialEdges: Edge[] = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    sourceHandle: 'trigger-output',
    targetHandle: 'condition-input',
    type: 'smoothstep',
    animated: true,
  },
  {
    id: 'e2-3',
    source: '2',
    target: '3',
    sourceHandle: 'condition-true',
    targetHandle: 'effect-input',
    type: 'smoothstep',
    animated: true,
  },
];

interface VisualEditorProps {
  entityId?: string;
  onSave?: (nodes: Node[], edges: Edge[]) => void;
  showResults?: boolean;
  resultsComponent?: React.ReactNode;
}

const VisualEditor: React.FC<VisualEditorProps> = ({
  entityId,
  onSave,
  showResults = false,
  resultsComponent
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [connectionErrors, setConnectionErrors] = useState<string[]>([]);
  const [dataFlowAnalysis, setDataFlowAnalysis] = useState<any>(null);
  const [executionStates, setExecutionStates] = useState<Map<string, ExecutionState>>(new Map());
  const [isExecuting, setIsExecuting] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchPosition, setSearchPosition] = useState({ x: 0, y: 0 });
  const [showGrid, setShowGrid] = useState(true);
  const [showMinimap, setShowMinimap] = useState(true);
  const [useModernUI, setUseModernUI] = useState(true);
  const [resultsOpen, setResultsOpen] = useState(showResults || false);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const executionEngine = useRef<VisualExecutionEngine | null>(null);
  const lastClickTime = useRef<number>(0);

  // 初始化执行引擎
  useEffect(() => {
    if (!executionEngine.current) {
      executionEngine.current = new VisualExecutionEngine((states) => {
        setExecutionStates(states);
      });
    }

    // 更新图数据
    executionEngine.current.setGraph(nodes, edges);
  }, [nodes, edges]);

  // 处理连接创建
  const onConnect = useCallback(
    (params: Connection) => {
      // 验证连接
      const sourceNode = nodes.find(n => n.id === params.source);
      const targetNode = nodes.find(n => n.id === params.target);

      if (sourceNode && targetNode && params.sourceHandle && params.targetHandle) {
        const validation = validateConnection(
          sourceNode,
          params.sourceHandle,
          targetNode,
          params.targetHandle
        );

        if (!validation.isValid) {
          setConnectionErrors(prev => [...prev, validation.error || '连接无效']);
          return;
        }
      }

      const newEdge = {
        ...params,
        type: 'smoothstep',
        animated: true,
        id: `e${params.source}-${params.target}-${Date.now()}`,
      };
      setEdges((eds) => addEdge(newEdge, eds));

      // 清除错误
      setConnectionErrors([]);
    },
    [nodes, setEdges]
  );

  // 处理节点选择
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  // 处理画布点击（取消选择）
  const onPaneClick = useCallback((event: React.MouseEvent) => {
    const currentTime = Date.now();
    const timeDiff = currentTime - lastClickTime.current;

    // 检测双击（300ms内的两次点击）
    if (timeDiff < 300) {
      // 双击打开搜索
      if (reactFlowInstance && reactFlowWrapper.current) {
        const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });
        setSearchPosition(position);
        setSearchOpen(true);
      }
    } else {
      // 单击取消选择
      setSelectedNode(null);
    }

    lastClickTime.current = currentTime;
  }, [reactFlowInstance]);

  // 处理拖拽放置新节点
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowWrapper.current || !reactFlowInstance) {
        return;
      }

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (!type) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: {
          label: `New ${type}`,
          description: `新的${type}节点`,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // 处理搜索节点选择
  const handleSearchNodeSelect = useCallback(
    (nodeType: string, position: { x: number; y: number }) => {
      const newNode: Node = {
        id: `${nodeType}-${Date.now()}`,
        type: nodeType,
        position,
        data: {
          label: `New ${nodeType}`,
          description: `新的${nodeType}节点`,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [setNodes]
  );

  // 工具栏处理函数
  const handleZoomIn = useCallback(() => {
    reactFlowInstance?.zoomIn();
  }, [reactFlowInstance]);

  const handleZoomOut = useCallback(() => {
    reactFlowInstance?.zoomOut();
  }, [reactFlowInstance]);

  const handleFitView = useCallback(() => {
    reactFlowInstance?.fitView();
  }, [reactFlowInstance]);

  const handleSelectAll = useCallback(() => {
    setNodes((nds) => nds.map(node => ({ ...node, selected: true })));
  }, [setNodes]);

  const handleDeleteSelected = useCallback(() => {
    setNodes((nds) => nds.filter(node => !node.selected));
    setEdges((eds) => eds.filter(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      return sourceNode && targetNode && !sourceNode.selected && !targetNode.selected;
    }));
  }, [setNodes, setEdges, nodes]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter(node => node.id !== nodeId));
    setEdges((eds) => eds.filter(edge => edge.source !== nodeId && edge.target !== nodeId));
  }, [setNodes, setEdges]);

  const handleNodeDuplicate = useCallback((nodeId: string) => {
    const nodeToDuplicate = nodes.find(node => node.id === nodeId);
    if (nodeToDuplicate) {
      const newNode: Node = {
        ...nodeToDuplicate,
        id: `${nodeToDuplicate.type}-${Date.now()}`,
        position: {
          x: nodeToDuplicate.position.x + 50,
          y: nodeToDuplicate.position.y + 50,
        },
        selected: false,
      };
      setNodes((nds) => nds.concat(newNode));
    }
  }, [nodes, setNodes]);

  // 处理节点更新
  const onNodeUpdate = useCallback(
    (nodeId: string, newData: any) => {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, ...newData } }
            : node
        )
      );
    },
    [setNodes]
  );

  // 分析数据流
  const analyzeFlow = useCallback(() => {
    const analysis = analyzeDataFlow(nodes, edges);
    setDataFlowAnalysis(analysis);
    return analysis;
  }, [nodes, edges]);

  // 执行控制
  const handleExecute = useCallback(async () => {
    if (!executionEngine.current) return;

    setIsExecuting(true);
    try {
      await executionEngine.current.start();
    } finally {
      setIsExecuting(false);
    }
  }, []);

  const handleStopExecution = useCallback(() => {
    if (executionEngine.current) {
      executionEngine.current.stop();
      setIsExecuting(false);
    }
  }, []);

  // 处理保存
  const handleSave = useCallback(() => {
    const analysis = analyzeFlow();
    if (analysis.isValid) {
      if (onSave) {
        onSave(nodes, edges);
      }
    } else {
      console.warn('数据流验证失败:', analysis.errors);
    }
  }, [nodes, edges, onSave, analyzeFlow]);

  const selectedNodesCount = nodes.filter(node => node.selected).length;

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 工具栏 */}
      <EditorToolbar
        onSave={handleSave}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onFitView={handleFitView}
        onToggleGrid={() => setShowGrid(!showGrid)}
        onToggleMinimap={() => setShowMinimap(!showMinimap)}
        onToggleResults={() => setResultsOpen(!resultsOpen)}
        onExecute={handleExecute}
        onStopExecution={handleStopExecution}
        onSelectAll={handleSelectAll}
        onDelete={handleDeleteSelected}
        isExecuting={isExecuting}
        showGrid={showGrid}
        showMinimap={showMinimap}
        showResults={resultsOpen}
        selectedNodesCount={selectedNodesCount}
        totalNodesCount={nodes.length}
      />

      <Box sx={{
        flex: 1,
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* 节点面板 */}
        <Paper sx={{
          width: useModernUI ? 280 : 250,
          borderRadius: 0,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderRight: 1,
          borderColor: 'divider',
          minHeight: 0 // 确保flex子元素可以收缩
        }}>
          {useModernUI ? (
            <ModernNodePalette />
          ) : (
            <>
              <Typography variant="h6" gutterBottom sx={{ p: 2, pb: 1 }}>
                节点库
              </Typography>
              <Box sx={{ flex: 1, overflow: 'auto', px: 2 }}>
                <NodePalette />
              </Box>
            </>
          )}
        </Paper>

        {/* 主编辑区域 */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          position: 'relative',
          minWidth: 0 // 确保flex子元素可以收缩
        }}>
        {/* 错误提示 */}
        <Collapse in={connectionErrors.length > 0}>
          <Alert
            severity="error"
            onClose={() => setConnectionErrors([])}
            sx={{ mx: 1, borderRadius: 0, flexShrink: 0 }}
          >
            连接错误: {connectionErrors.join(', ')}
          </Alert>
        </Collapse>

        {/* 数据流分析结果 */}
        <Collapse in={dataFlowAnalysis && !dataFlowAnalysis.isValid}>
          <Alert
            severity="warning"
            sx={{ mx: 1, borderRadius: 0, flexShrink: 0 }}
          >
            数据流问题: {dataFlowAnalysis?.errors.join(', ') || ''}
          </Alert>
        </Collapse>

        {/* ReactFlow画布 */}
        <Box sx={{
          flex: 1,
          position: 'relative',
          overflow: 'hidden'
        }} ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes.map(node => ({
            ...node,
            data: {
              ...node.data,
              isExecuting: executionStates.get(node.id)?.status === 'executing',
              executionProgress: executionStates.get(node.id)?.progress || 0,
              executionStatus: executionStates.get(node.id)?.status || 'idle',
            }
          }))}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          onPaneClick={onPaneClick}
          onDragOver={onDragOver}
          onDrop={onDrop}
          onInit={setReactFlowInstance}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          attributionPosition="bottom-left"
        >
          {showGrid && <Background variant={BackgroundVariant.Dots} gap={20} size={1} />}
          <Controls />
          {showMinimap && (
            <MiniMap
              nodeColor={(node) => {
                switch (node.type) {
                  case 'trigger':
                    return '#ff6b6b';
                  case 'condition':
                    return '#4ecdc4';
                  case 'effect':
                    return '#45b7d1';
                  case 'calculation':
                    return '#96ceb4';
                  default:
                    return '#eee';
                }
              }}
              nodeStrokeWidth={3}
              zoomable
              pannable
            />
          )}
        </ReactFlow>
        </Box>
      </Box>

        {/* 属性面板 */}
        <Paper sx={{
          width: useModernUI ? 320 : 300,
          borderRadius: 0,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderLeft: 1,
          borderColor: 'divider',
          minHeight: 0 // 确保flex子元素可以收缩
        }}>
          {useModernUI ? (
            <ModernNodeInspector
              selectedNode={selectedNode}
              onNodeUpdate={onNodeUpdate}
              onSave={handleSave}
              onExecute={handleExecute}
              onStopExecution={handleStopExecution}
              isExecuting={isExecuting}
              executionStates={executionStates}
              onNodeDelete={handleNodeDelete}
              onNodeDuplicate={handleNodeDuplicate}
            />
          ) : (
            <>
              <Typography variant="h6" gutterBottom sx={{ flexShrink: 0, p: 2, pb: 1 }}>
                属性面板
              </Typography>
              <Box sx={{ flex: 1, overflow: 'auto', px: 2 }}>
                <NodeInspector
                  selectedNode={selectedNode}
                  onNodeUpdate={onNodeUpdate}
                  onSave={handleSave}
                  onExecute={handleExecute}
                  onStopExecution={handleStopExecution}
                  isExecuting={isExecuting}
                  executionStates={executionStates}
                />
              </Box>
            </>
          )}
        </Paper>
      </Box>

      {/* 节点搜索对话框 */}
      <NodeSearch
        open={searchOpen}
        onClose={() => setSearchOpen(false)}
        onNodeSelect={handleSearchNodeSelect}
        position={searchPosition}
      />

      {/* 侧滑Results面板 */}
      {showResults && resultsComponent && (
        <ResizableSidePanel
          open={resultsOpen}
          onClose={() => setResultsOpen(false)}
          title="执行结果"
          defaultWidth={400}
          minWidth={300}
          maxWidth={800}
          side="right"
        >
          {resultsComponent}
        </ResizableSidePanel>
      )}
    </Box>
  );
};

// 包装组件以提供ReactFlowProvider
const VisualEditorWrapper: React.FC<VisualEditorProps> = (props) => {
  return (
    <ReactFlowProvider>
      <VisualEditor {...props} />
    </ReactFlowProvider>
  );
};

export default VisualEditorWrapper;
