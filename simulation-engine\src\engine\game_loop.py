"""
游戏循环引擎
管理模拟的时间推进和更新逻辑
"""

import time
import asyncio
from typing import List, Callable, Optional
from .event_system import EventSystem, EventType
from .state_manager import StateManager


class GameLoop:
    """游戏循环管理器"""

    def __init__(self, event_system: EventSystem, state_manager: StateManager):
        self.event_system = event_system
        self.state_manager = state_manager
        
        self._is_running = False
        self._is_paused = False
        self._current_time = 0.0
        self._tick_rate = 60.0  # 每秒tick数
        self._tick_duration = 1.0 / self._tick_rate
        self._tick_count = 0
        
        self._update_callbacks: List[Callable[[float], None]] = []
        self._fixed_update_callbacks: List[Callable[[float], None]] = []

    def set_tick_rate(self, tick_rate: float) -> None:
        """设置tick频率"""
        self._tick_rate = max(1.0, tick_rate)
        self._tick_duration = 1.0 / self._tick_rate

    def get_tick_rate(self) -> float:
        """获取tick频率"""
        return self._tick_rate

    def get_current_time(self) -> float:
        """获取当前模拟时间"""
        return self._current_time

    def get_tick_count(self) -> int:
        """获取tick计数"""
        return self._tick_count

    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._is_running

    def is_paused(self) -> bool:
        """检查是否暂停"""
        return self._is_paused

    def add_update_callback(self, callback: Callable[[float], None]) -> None:
        """添加更新回调"""
        self._update_callbacks.append(callback)

    def remove_update_callback(self, callback: Callable[[float], None]) -> None:
        """移除更新回调"""
        if callback in self._update_callbacks:
            self._update_callbacks.remove(callback)

    def add_fixed_update_callback(self, callback: Callable[[float], None]) -> None:
        """添加固定更新回调"""
        self._fixed_update_callbacks.append(callback)

    def remove_fixed_update_callback(self, callback: Callable[[float], None]) -> None:
        """移除固定更新回调"""
        if callback in self._fixed_update_callbacks:
            self._fixed_update_callbacks.remove(callback)

    async def start(self, duration: Optional[float] = None) -> None:
        """开始游戏循环"""
        if self._is_running:
            return

        self._is_running = True
        self._is_paused = False
        start_time = time.time()
        target_end_time = start_time + duration if duration else None

        print(f"Starting game loop at {self._tick_rate} ticks per second")
        
        try:
            while self._is_running:
                if not self._is_paused:
                    # 更新时间
                    self._current_time += self._tick_duration
                    self._tick_count += 1
                    
                    # 更新事件系统时间
                    self.event_system.set_current_time(self._current_time)
                    
                    # 发出tick开始事件
                    self.event_system.create_and_emit(
                        EventType.TURN_START,
                        data={"tick": self._tick_count, "time": self._current_time}
                    )
                    
                    # 执行固定更新回调
                    for callback in self._fixed_update_callbacks:
                        try:
                            callback(self._tick_duration)
                        except Exception as e:
                            print(f"Error in fixed update callback: {e}")
                    
                    # 执行更新回调
                    for callback in self._update_callbacks:
                        try:
                            callback(self._current_time)
                        except Exception as e:
                            print(f"Error in update callback: {e}")
                    
                    # 发出tick结束事件
                    self.event_system.create_and_emit(
                        EventType.TURN_END,
                        data={"tick": self._tick_count, "time": self._current_time}
                    )
                    
                    # 检查是否达到结束时间
                    if target_end_time and time.time() >= target_end_time:
                        break

                # 等待下一个tick
                await asyncio.sleep(self._tick_duration)

        except Exception as e:
            print(f"Error in game loop: {e}")
        finally:
            self._is_running = False
            print(f"Game loop stopped after {self._tick_count} ticks ({self._current_time:.2f}s)")

    def pause(self) -> None:
        """暂停游戏循环"""
        self._is_paused = True

    def resume(self) -> None:
        """恢复游戏循环"""
        self._is_paused = False

    def stop(self) -> None:
        """停止游戏循环"""
        self._is_running = False

    def reset(self) -> None:
        """重置游戏循环"""
        self.stop()
        self._current_time = 0.0
        self._tick_count = 0
        self.event_system.clear_history()

    def step(self) -> None:
        """单步执行（调试用）"""
        if not self._is_running:
            return
            
        self._current_time += self._tick_duration
        self._tick_count += 1
        
        self.event_system.set_current_time(self._current_time)
        
        # 执行回调
        for callback in self._fixed_update_callbacks:
            try:
                callback(self._tick_duration)
            except Exception as e:
                print(f"Error in fixed update callback: {e}")
        
        for callback in self._update_callbacks:
            try:
                callback(self._current_time)
            except Exception as e:
                print(f"Error in update callback: {e}")
