"""
实体相关数据模型
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class EntityType(str, Enum):
    """实体类型枚举"""
    CHARACTER = "CHARACTER"
    SKILL = "SKILL"
    EQUIPMENT = "EQUIPMENT"
    BUFF = "BUFF"
    MONSTER = "MONSTER"
    ITEM = "ITEM"
    CONTAINER = "CONTAINER"
    GRID = "GRID"


class Attribute(BaseModel):
    """属性定义"""
    name: str
    value: float
    min: Optional[float] = None
    max: Optional[float] = None
    description: Optional[str] = None


class Tag(BaseModel):
    """标签定义"""
    name: str
    category: Optional[str] = None
    description: Optional[str] = None


class Entity(BaseModel):
    """实体定义"""
    id: str
    name: str
    type: EntityType
    description: Optional[str] = None
    attributes: Dict[str, Attribute] = Field(default_factory=dict)
    tags: List[Tag] = Field(default_factory=list)
    logic: List[Any] = Field(default_factory=list)  # LogicNode列表，避免循环导入
    connections: List[Any] = Field(default_factory=list)  # Connection列表
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    def get_attribute_value(self, name: str) -> float:
        """获取属性值"""
        if name in self.attributes:
            return self.attributes[name].value
        return 0.0

    def set_attribute_value(self, name: str, value: float) -> None:
        """设置属性值"""
        if name in self.attributes:
            attr = self.attributes[name]
            # 应用最小值和最大值限制
            if attr.min is not None:
                value = max(value, attr.min)
            if attr.max is not None:
                value = min(value, attr.max)
            self.attributes[name].value = value
        else:
            # 创建新属性
            self.attributes[name] = Attribute(name=name, value=value)

    def has_tag(self, tag_name: str) -> bool:
        """检查是否有指定标签"""
        return any(tag.name == tag_name for tag in self.tags)

    def add_tag(self, tag: Tag) -> None:
        """添加标签"""
        if not self.has_tag(tag.name):
            self.tags.append(tag)

    def remove_tag(self, tag_name: str) -> None:
        """移除标签"""
        self.tags = [tag for tag in self.tags if tag.name != tag_name]

    class Config:
        """Pydantic配置"""
        use_enum_values = True
