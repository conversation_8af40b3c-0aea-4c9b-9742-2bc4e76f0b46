import React from 'react';
import { NodeProps } from 'reactflow';
import { PlayArrow } from '@mui/icons-material';
import AdvancedBaseNode from './AdvancedBaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const TriggerNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('trigger');

  return (
    <AdvancedBaseNode
      {...props}
      color={config?.color || '#ff6b6b'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<PlayArrow sx={{ fontSize: 16 }} />}
    />
  );
};

export default TriggerNode;
