"""
示例数据创建工具
用于创建测试用的实体和模拟
"""

from typing import List
from ..models.entity import Entity, EntityType, Attribute, Tag


def create_sample_entities() -> List[Entity]:
    """创建示例实体用于测试"""
    entities = []

    # 创建玩家角色
    player = Entity(
        id="player_warrior",
        name="Warrior",
        type=EntityType.CHARACTER,
        description="A brave warrior with high attack power",
        attributes={
            "health": Attribute(name="health", value=1000, min=0, max=1000),
            "max_health": Attribute(name="max_health", value=1000),
            "attack_power": Attribute(name="attack_power", value=150),
            "attack_speed": Attribute(name="attack_speed", value=1.2),
            "defense": Attribute(name="defense", value=50),
        },
        tags=[
            Tag(name="player", category="faction"),
            Tag(name="melee", category="combat"),
            Tag(name="warrior", category="class"),
        ]
    )
    entities.append(player)

    # 创建法师角色
    mage = Entity(
        id="player_mage",
        name="Mage",
        type=EntityType.CHARACTER,
        description="A powerful mage with magical abilities",
        attributes={
            "health": Attribute(name="health", value=600, min=0, max=600),
            "max_health": Attribute(name="max_health", value=600),
            "attack_power": Attribute(name="attack_power", value=200),
            "attack_speed": Attribute(name="attack_speed", value=0.8),
            "mana": Attribute(name="mana", value=500, min=0, max=500),
            "max_mana": Attribute(name="max_mana", value=500),
        },
        tags=[
            Tag(name="player", category="faction"),
            Tag(name="ranged", category="combat"),
            Tag(name="mage", category="class"),
            Tag(name="magic", category="damage_type"),
        ]
    )
    entities.append(mage)

    # 创建训练假人
    dummy = Entity(
        id="training_dummy",
        name="Training Dummy",
        type=EntityType.MONSTER,
        description="A stationary target for DPS testing",
        attributes={
            "health": Attribute(name="health", value=10000, min=0, max=10000),
            "max_health": Attribute(name="max_health", value=10000),
            "defense": Attribute(name="defense", value=0),
        },
        tags=[
            Tag(name="dummy", category="type"),
            Tag(name="stationary", category="behavior"),
        ]
    )
    entities.append(dummy)

    # 创建史莱姆怪物
    slime = Entity(
        id="green_slime",
        name="Green Slime",
        type=EntityType.MONSTER,
        description="A weak but numerous enemy",
        attributes={
            "health": Attribute(name="health", value=200, min=0, max=200),
            "max_health": Attribute(name="max_health", value=200),
            "attack_power": Attribute(name="attack_power", value=30),
            "attack_speed": Attribute(name="attack_speed", value=0.5),
        },
        tags=[
            Tag(name="monster", category="faction"),
            Tag(name="slime", category="type"),
            Tag(name="weak", category="difficulty"),
        ]
    )
    entities.append(slime)

    # 创建龙怪物
    dragon = Entity(
        id="fire_dragon",
        name="Fire Dragon",
        type=EntityType.MONSTER,
        description="A powerful dragon with fire breath",
        attributes={
            "health": Attribute(name="health", value=5000, min=0, max=5000),
            "max_health": Attribute(name="max_health", value=5000),
            "attack_power": Attribute(name="attack_power", value=300),
            "attack_speed": Attribute(name="attack_speed", value=0.3),
            "defense": Attribute(name="defense", value=100),
        },
        tags=[
            Tag(name="monster", category="faction"),
            Tag(name="dragon", category="type"),
            Tag(name="boss", category="difficulty"),
            Tag(name="fire", category="element"),
        ]
    )
    entities.append(dragon)

    # 创建治疗技能
    heal_skill = Entity(
        id="heal_spell",
        name="Heal",
        type=EntityType.SKILL,
        description="Restores health to the target",
        attributes={
            "healing_power": Attribute(name="healing_power", value=200),
            "mana_cost": Attribute(name="mana_cost", value=50),
            "cooldown": Attribute(name="cooldown", value=3.0),
            "cast_time": Attribute(name="cast_time", value=1.5),
        },
        tags=[
            Tag(name="healing", category="type"),
            Tag(name="magic", category="school"),
            Tag(name="targeted", category="targeting"),
        ]
    )
    entities.append(heal_skill)

    # 创建火球技能
    fireball_skill = Entity(
        id="fireball_spell",
        name="Fireball",
        type=EntityType.SKILL,
        description="Launches a fireball at the target",
        attributes={
            "damage": Attribute(name="damage", value=250),
            "mana_cost": Attribute(name="mana_cost", value=80),
            "cooldown": Attribute(name="cooldown", value=2.0),
            "cast_time": Attribute(name="cast_time", value=2.0),
        },
        tags=[
            Tag(name="damage", category="type"),
            Tag(name="fire", category="element"),
            Tag(name="magic", category="school"),
            Tag(name="projectile", category="targeting"),
        ]
    )
    entities.append(fireball_skill)

    # 创建护盾Buff
    shield_buff = Entity(
        id="magic_shield",
        name="Magic Shield",
        type=EntityType.BUFF,
        description="Absorbs incoming damage",
        attributes={
            "shield_amount": Attribute(name="shield_amount", value=500),
            "duration": Attribute(name="duration", value=30.0),
        },
        tags=[
            Tag(name="shield", category="type"),
            Tag(name="protection", category="effect"),
            Tag(name="magic", category="school"),
        ]
    )
    entities.append(shield_buff)

    return entities
