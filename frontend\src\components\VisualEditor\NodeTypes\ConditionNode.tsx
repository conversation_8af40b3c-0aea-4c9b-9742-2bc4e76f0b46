import React from 'react';
import { NodeProps } from 'reactflow';
import { Help } from '@mui/icons-material';
import BaseNode from './BaseNode';
import { NodeData, getNodeTypeConfig } from './index';

const ConditionNode: React.FC<NodeProps<NodeData>> = (props) => {
  const config = getNodeTypeConfig('condition');
  
  return (
    <BaseNode
      {...props}
      color={config?.color || '#4ecdc4'}
      inputs={config?.inputs || []}
      outputs={config?.outputs || []}
      icon={<Help sx={{ fontSize: 16 }} />}
    />
  );
};

export default ConditionNode;
