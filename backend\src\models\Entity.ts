/**
 * Entity MongoDB model
 */

import mongoose, { Schema, Document } from 'mongoose';
import { Entity as IEntity, EntityType, Attribute, Tag, LogicNode, Connection } from '../../../shared/types';

// Mongoose document interface
export interface EntityDocument extends Omit<IEntity, 'id'>, Document {
  _id: string;
}

// Attribute schema
const AttributeSchema = new Schema<Attribute>({
  name: { type: String, required: true },
  value: { type: Number, required: true },
  min: { type: Number },
  max: { type: Number },
  description: { type: String }
}, { _id: false });

// Tag schema
const TagSchema = new Schema<Tag>({
  name: { type: String, required: true },
  category: { type: String },
  description: { type: String }
}, { _id: false });

// Logic node schema
const LogicNodeSchema = new Schema<LogicNode>({
  id: { type: String, required: true },
  type: { type: String, required: true },
  name: { type: String, required: true },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true }
  },
  inputs: { type: Schema.Types.Mixed, default: {} },
  outputs: { type: Schema.Types.Mixed, default: {} },
  parameters: { type: Schema.Types.Mixed, default: {} },
  description: { type: String }
}, { _id: false });

// Connection schema
const ConnectionSchema = new Schema<Connection>({
  id: { type: String, required: true },
  from: {
    nodeId: { type: String, required: true },
    outputPin: { type: String, required: true }
  },
  to: {
    nodeId: { type: String, required: true },
    inputPin: { type: String, required: true }
  }
}, { _id: false });

// Entity schema
const EntitySchema = new Schema<EntityDocument>({
  name: { type: String, required: true },
  type: { 
    type: String, 
    required: true,
    enum: Object.values(EntityType)
  },
  description: { type: String },
  attributes: {
    type: Map,
    of: AttributeSchema,
    default: new Map()
  },
  tags: [TagSchema],
  logic: [LogicNodeSchema],
  connections: [ConnectionSchema],
  metadata: { type: Schema.Types.Mixed }
}, {
  timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
EntitySchema.index({ name: 1 });
EntitySchema.index({ type: 1 });
EntitySchema.index({ 'tags.name': 1 });

export const EntityModel = mongoose.model<EntityDocument>('Entity', EntitySchema);
