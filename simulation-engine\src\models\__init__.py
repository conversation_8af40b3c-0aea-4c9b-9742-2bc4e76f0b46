"""
数据模型模块
定义模拟引擎使用的核心数据结构
"""

from .entity import Entity, EntityType, Attribute, Tag
from .logic_node import LogicNode, NodeType, TriggerType, Connection
from .simulation import Simulation, SimulationSettings, SimulationResult, DataPoint, SimulationEvent

__all__ = [
    'Entity',
    'EntityType', 
    'Attribute',
    'Tag',
    'LogicNode',
    'NodeType',
    'TriggerType',
    'Connection',
    'Simulation',
    'SimulationSettings',
    'SimulationResult',
    'DataPoint',
    'SimulationEvent'
]
