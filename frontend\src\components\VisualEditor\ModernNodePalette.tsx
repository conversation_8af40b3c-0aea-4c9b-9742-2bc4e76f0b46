import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  <PERSON>bs,
  Tab,
  Badge,
  Collapse,
} from '@mui/material';
import {
  Search,
  PlayArrow,
  Help,
  FlashOn,
  Calculate,
  Storage,
  Looks,
  Star,
  StarBorder,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';
import { getNodeTypesByCategory, NodeTypeConfig } from './NodeTypes';

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  trigger: <PlayArrow />,
  condition: <Help />,
  effect: <FlashOn />,
  calculation: <Calculate />,
  variable: <Storage />,
  constant: <Looks />,
};

// 分类图标映射
const categoryIconMap: Record<string, React.ReactNode> = {
  Events: <PlayArrow />,
  Logic: <Help />,
  Actions: <FlashOn />,
  Math: <Calculate />,
  Data: <Storage />,
};

// 本地存储键
const FAVORITES_KEY = 'nodeFavorites';
const RECENT_NODES_KEY = 'recentNodes';

const getFavorites = (): string[] => {
  try {
    const favorites = localStorage.getItem(FAVORITES_KEY);
    return favorites ? JSON.parse(favorites) : [];
  } catch {
    return [];
  }
};

const getRecentNodes = (): string[] => {
  try {
    const recent = localStorage.getItem(RECENT_NODES_KEY);
    return recent ? JSON.parse(recent) : [];
  } catch {
    return [];
  }
};

const addToFavorites = (nodeType: string) => {
  const favorites = getFavorites();
  if (!favorites.includes(nodeType)) {
    const newFavorites = [...favorites, nodeType];
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(newFavorites));
  }
};

const removeFromFavorites = (nodeType: string) => {
  const favorites = getFavorites();
  const newFavorites = favorites.filter(fav => fav !== nodeType);
  localStorage.setItem(FAVORITES_KEY, JSON.stringify(newFavorites));
};

const addToRecent = (nodeType: string) => {
  const recent = getRecentNodes();
  const newRecent = [nodeType, ...recent.filter(item => item !== nodeType)].slice(0, 10);
  localStorage.setItem(RECENT_NODES_KEY, JSON.stringify(newRecent));
};

interface ModernNodePaletteProps {
  onNodeDragStart?: (event: React.DragEvent, nodeType: string) => void;
}

const ModernNodePalette: React.FC<ModernNodePaletteProps> = ({ onNodeDragStart }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [favorites, setFavorites] = useState<string[]>(getFavorites());
  const [recentNodes, setRecentNodes] = useState<string[]>(getRecentNodes());
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({
    Events: true,
    Logic: true,
    Actions: true,
    Math: true,
    Data: true,
  });

  const nodesByCategory = getNodeTypesByCategory();
  const allNodes = Object.values(nodesByCategory).flat();

  // 过滤节点
  const filteredNodes = searchQuery
    ? allNodes.filter(node =>
        node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        node.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : allNodes;

  // 获取收藏的节点
  const favoriteNodes = allNodes.filter(node => favorites.includes(node.type));

  // 获取最近使用的节点
  const recentNodeConfigs = recentNodes
    .map(nodeType => allNodes.find(node => node.type === nodeType))
    .filter(Boolean) as NodeTypeConfig[];

  const handleDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
    addToRecent(nodeType);
    setRecentNodes(getRecentNodes());
    onNodeDragStart?.(event, nodeType);
  };

  const toggleFavorite = (nodeType: string) => {
    if (favorites.includes(nodeType)) {
      removeFromFavorites(nodeType);
      setFavorites(favorites.filter(fav => fav !== nodeType));
    } else {
      addToFavorites(nodeType);
      setFavorites([...favorites, nodeType]);
    }
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const renderNodeCard = (nodeConfig: NodeTypeConfig, showCategory = false) => (
    <Card
      key={nodeConfig.type}
      sx={{
        cursor: 'grab',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 3,
        },
        '&:active': {
          cursor: 'grabbing',
          transform: 'translateY(0)',
        },
        position: 'relative',
        minHeight: 80,
      }}
      draggable
      onDragStart={(e) => handleDragStart(e, nodeConfig.type)}
    >
      <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <Box
            sx={{
              color: nodeConfig.color,
              display: 'flex',
              alignItems: 'center',
              mt: 0.5,
            }}
          >
            {iconMap[nodeConfig.type]}
          </Box>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 'medium',
                fontSize: 12,
                lineHeight: 1.2,
                mb: 0.5,
              }}
            >
              {nodeConfig.label}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                fontSize: 10,
                lineHeight: 1.2,
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
            >
              {nodeConfig.description}
            </Typography>
            {showCategory && (
              <Chip
                label={nodeConfig.category}
                size="small"
                variant="outlined"
                sx={{ fontSize: 9, height: 16, mt: 0.5 }}
              />
            )}
          </Box>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              toggleFavorite(nodeConfig.type);
            }}
            sx={{ p: 0.5 }}
          >
            {favorites.includes(nodeConfig.type) ? (
              <Star sx={{ fontSize: 14, color: 'warning.main' }} />
            ) : (
              <StarBorder sx={{ fontSize: 14 }} />
            )}
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  );

  const tabLabels = ['全部', '收藏', '最近'];

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 搜索框 */}
      <Box sx={{ p: 2, pb: 1 }}>
        <TextField
          fullWidth
          size="small"
          placeholder="搜索节点..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search sx={{ fontSize: 18 }} />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              fontSize: 12,
            }
          }}
        />
      </Box>

      {/* 标签页 */}
      <Box sx={{ px: 2 }}>
        <Tabs
          value={selectedTab}
          onChange={(_, newValue) => setSelectedTab(newValue)}
          variant="fullWidth"
          sx={{
            minHeight: 36,
            '& .MuiTab-root': {
              minHeight: 36,
              fontSize: 11,
              textTransform: 'none',
            }
          }}
        >
          {tabLabels.map((label, index) => (
            <Tab
              key={label}
              label={
                <Badge
                  badgeContent={
                    index === 1 ? favorites.length :
                    index === 2 ? recentNodes.length : null
                  }
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      fontSize: 9,
                      height: 14,
                      minWidth: 14,
                    }
                  }}
                >
                  {label}
                </Badge>
              }
            />
          ))}
        </Tabs>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2, pt: 1 }}>
        {selectedTab === 0 && (
          // 全部节点 - 按分类显示
          searchQuery ? (
            <Grid container spacing={1}>
              {filteredNodes.map(nodeConfig => (
                <Grid item xs={12} key={nodeConfig.type}>
                  {renderNodeCard(nodeConfig, true)}
                </Grid>
              ))}
            </Grid>
          ) : (
            Object.entries(nodesByCategory).map(([category, nodes]) => (
              <Box key={category} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    mb: 1,
                    p: 0.5,
                    borderRadius: 1,
                    '&:hover': { backgroundColor: 'action.hover' },
                  }}
                  onClick={() => toggleCategory(category)}
                >
                  <Box sx={{ color: 'text.secondary', mr: 1 }}>
                    {categoryIconMap[category]}
                  </Box>
                  <Typography variant="subtitle2" sx={{ flex: 1, fontSize: 12 }}>
                    {category}
                  </Typography>
                  <Badge badgeContent={nodes.length} color="primary" sx={{
                    '& .MuiBadge-badge': { fontSize: 9, height: 14, minWidth: 14 }
                  }}>
                    {expandedCategories[category] ? <ExpandLess /> : <ExpandMore />}
                  </Badge>
                </Box>
                <Collapse in={expandedCategories[category]}>
                  <Grid container spacing={1}>
                    {nodes.map(nodeConfig => (
                      <Grid item xs={12} key={nodeConfig.type}>
                        {renderNodeCard(nodeConfig)}
                      </Grid>
                    ))}
                  </Grid>
                </Collapse>
              </Box>
            ))
          )
        )}

        {selectedTab === 1 && (
          // 收藏节点
          <Grid container spacing={1}>
            {favoriteNodes.length === 0 ? (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                  还没有收藏的节点
                </Typography>
              </Grid>
            ) : (
              favoriteNodes.map(nodeConfig => (
                <Grid item xs={12} key={nodeConfig.type}>
                  {renderNodeCard(nodeConfig, true)}
                </Grid>
              ))
            )}
          </Grid>
        )}

        {selectedTab === 2 && (
          // 最近使用
          <Grid container spacing={1}>
            {recentNodeConfigs.length === 0 ? (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                  还没有使用过节点
                </Typography>
              </Grid>
            ) : (
              recentNodeConfigs.map(nodeConfig => (
                <Grid item xs={12} key={nodeConfig.type}>
                  {renderNodeCard(nodeConfig, true)}
                </Grid>
              ))
            )}
          </Grid>
        )}
      </Box>

      {/* 提示信息 */}
      <Box sx={{ p: 2, pt: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" sx={{ fontSize: 10 }}>
          💡 拖拽节点到画布中创建，双击画布打开搜索
        </Typography>
      </Box>
    </Box>
  );
};

export default ModernNodePalette;
