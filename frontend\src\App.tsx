import React, { useState } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Tabs,
  Tab
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Import pages
import EntityManager from './pages/EntityManager';
import SimulationEditor from './pages/SimulationEditor';
import SimulationResults from './pages/SimulationResults';

const StyledContainer = styled(Container)(({ theme }) => ({
  // 移除了marginTop和marginBottom，因为现在使用flexbox布局
}));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

function App() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <AppBar position="static" sx={{ flexShrink: 0, minHeight: 48 }}>
        <Toolbar variant="dense" sx={{ minHeight: 48 }}>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontSize: '1.1rem' }}>
            Game Logic Preview
          </Typography>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="main navigation"
            sx={{
              ml: 2,
              '& .MuiTab-root': {
                minHeight: 48,
                color: 'rgba(255, 255, 255, 0.7)',
                '&.Mui-selected': {
                  color: 'white'
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: 'white'
              }
            }}
          >
            <Tab label="Entity Manager" {...a11yProps(0)} />
            <Tab label="Simulation Editor" {...a11yProps(1)} />
            <Tab label="Results" {...a11yProps(2)} />
          </Tabs>
          <Typography variant="body2" sx={{ opacity: 0.7, ml: 2 }}>
            v0.1.0
          </Typography>
        </Toolbar>
      </AppBar>

      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        <StyledContainer maxWidth="xl" sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          mt: 0,
          mb: 0,
          px: 1
        }}>

          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ height: '100%', overflow: 'auto' }}>
                <EntityManager />
              </Box>
            </TabPanel>
            <TabPanel value={tabValue} index={1}>
              <Box sx={{ height: '100%', overflow: 'hidden' }}>
                <SimulationEditor />
              </Box>
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
              <Box sx={{ height: '100%', overflow: 'auto' }}>
                <SimulationResults />
              </Box>
            </TabPanel>
          </Box>
        </StyledContainer>
      </Box>
    </Box>
  );
}

export default App;
