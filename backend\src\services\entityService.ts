/**
 * Entity service with fallback to in-memory storage
 */

import mongoose from 'mongoose';
import { EntityModel } from '../models/Entity';
import { Entity, EntityType } from '../../../shared/types';
import { logger } from '../utils/logger';

// In-memory storage as fallback
let inMemoryEntities: Entity[] = [];
let nextId = 1;

// Check if MongoDB is connected
function isMongoConnected(): boolean {
  return mongoose.connection.readyState === 1;
}

// Generate a simple ID for in-memory storage
function generateId(): string {
  return `entity_${nextId++}`;
}

// Convert MongoDB document to Entity interface
function documentToEntity(doc: any): Entity {
  return {
    id: doc._id.toString(),
    name: doc.name,
    type: doc.type,
    description: doc.description,
    attributes: doc.attributes || {},
    tags: doc.tags || [],
    logic: doc.logic || [],
    connections: doc.connections || [],
    metadata: doc.metadata,
    createdAt: doc.createdAt,
    updatedAt: doc.updatedAt
  };
}

export class EntityService {
  async getAllEntities(params?: {
    type?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    const { type, search, limit = 50, offset = 0 } = params || {};

    if (isMongoConnected()) {
      try {
        const filter: any = {};
        if (type && Object.values(EntityType).includes(type as EntityType)) {
          filter.type = type;
        }
        if (search) {
          filter.$or = [
            { name: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
          ];
        }

        const entities = await EntityModel
          .find(filter)
          .limit(Number(limit))
          .skip(Number(offset))
          .sort({ updatedAt: -1 });

        const total = await EntityModel.countDocuments(filter);

        return {
          entities: entities.map(documentToEntity),
          pagination: {
            total,
            limit: Number(limit),
            offset: Number(offset),
            hasMore: Number(offset) + Number(limit) < total
          }
        };
      } catch (error) {
        logger.error('MongoDB query failed, falling back to in-memory storage:', error);
      }
    }

    // Fallback to in-memory storage
    logger.info('Using in-memory storage for entities');
    let filteredEntities = [...inMemoryEntities];

    if (type) {
      filteredEntities = filteredEntities.filter(e => e.type === type);
    }
    if (search) {
      const searchLower = search.toLowerCase();
      filteredEntities = filteredEntities.filter(e => 
        e.name.toLowerCase().includes(searchLower) ||
        (e.description && e.description.toLowerCase().includes(searchLower))
      );
    }

    const total = filteredEntities.length;
    const paginatedEntities = filteredEntities
      .slice(Number(offset), Number(offset) + Number(limit));

    return {
      entities: paginatedEntities,
      pagination: {
        total,
        limit: Number(limit),
        offset: Number(offset),
        hasMore: Number(offset) + Number(limit) < total
      }
    };
  }

  async getEntityById(id: string): Promise<Entity | null> {
    if (isMongoConnected()) {
      try {
        const entity = await EntityModel.findById(id);
        return entity ? documentToEntity(entity) : null;
      } catch (error) {
        logger.error('MongoDB query failed, falling back to in-memory storage:', error);
      }
    }

    // Fallback to in-memory storage
    return inMemoryEntities.find(e => e.id === id) || null;
  }

  async createEntity(entityData: Omit<Entity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Entity> {
    if (isMongoConnected()) {
      try {
        const entity = new EntityModel(entityData);
        await entity.save();
        return documentToEntity(entity);
      } catch (error) {
        logger.error('MongoDB save failed, falling back to in-memory storage:', error);
      }
    }

    // Fallback to in-memory storage
    const newEntity: Entity = {
      ...entityData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    inMemoryEntities.push(newEntity);
    logger.info(`Created entity in memory: ${newEntity.name} (${newEntity.id})`);
    return newEntity;
  }

  async updateEntity(id: string, entityData: Partial<Entity>): Promise<Entity | null> {
    if (isMongoConnected()) {
      try {
        const entity = await EntityModel.findByIdAndUpdate(
          id,
          { ...entityData, updatedAt: new Date() },
          { new: true, runValidators: true }
        );
        return entity ? documentToEntity(entity) : null;
      } catch (error) {
        logger.error('MongoDB update failed, falling back to in-memory storage:', error);
      }
    }

    // Fallback to in-memory storage
    const entityIndex = inMemoryEntities.findIndex(e => e.id === id);
    if (entityIndex === -1) {
      return null;
    }

    inMemoryEntities[entityIndex] = {
      ...inMemoryEntities[entityIndex],
      ...entityData,
      id, // Ensure ID doesn't change
      updatedAt: new Date()
    };
    
    logger.info(`Updated entity in memory: ${inMemoryEntities[entityIndex].name} (${id})`);
    return inMemoryEntities[entityIndex];
  }

  async deleteEntity(id: string): Promise<boolean> {
    if (isMongoConnected()) {
      try {
        const result = await EntityModel.findByIdAndDelete(id);
        return !!result;
      } catch (error) {
        logger.error('MongoDB delete failed, falling back to in-memory storage:', error);
      }
    }

    // Fallback to in-memory storage
    const entityIndex = inMemoryEntities.findIndex(e => e.id === id);
    if (entityIndex === -1) {
      return false;
    }

    const deletedEntity = inMemoryEntities.splice(entityIndex, 1)[0];
    logger.info(`Deleted entity from memory: ${deletedEntity.name} (${id})`);
    return true;
  }

  async duplicateEntity(id: string): Promise<Entity | null> {
    const originalEntity = await this.getEntityById(id);
    if (!originalEntity) {
      return null;
    }

    const duplicateData = {
      ...originalEntity,
      name: `${originalEntity.name} (Copy)`
    };
    delete (duplicateData as any).id;
    delete (duplicateData as any).createdAt;
    delete (duplicateData as any).updatedAt;

    return this.createEntity(duplicateData);
  }

  // Initialize with some sample data for demo purposes
  initializeSampleData() {
    if (inMemoryEntities.length === 0) {
      const sampleEntities: Omit<Entity, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          name: "Warrior",
          type: EntityType.CHARACTER,
          description: "A brave warrior with high attack power",
          attributes: {
            health: { name: "health", value: 1000, min: 0, max: 1000 },
            attack_power: { name: "attack_power", value: 150 },
            attack_speed: { name: "attack_speed", value: 1.2 }
          },
          tags: [
            { name: "player", category: "faction" },
            { name: "melee", category: "combat" }
          ],
          logic: [],
          connections: []
        },
        {
          name: "Training Dummy",
          type: EntityType.MONSTER,
          description: "A stationary target for DPS testing",
          attributes: {
            health: { name: "health", value: 10000, min: 0, max: 10000 },
            defense: { name: "defense", value: 0 }
          },
          tags: [
            { name: "dummy", category: "type" },
            { name: "stationary", category: "behavior" }
          ],
          logic: [],
          connections: []
        }
      ];

      sampleEntities.forEach(entityData => {
        this.createEntity(entityData);
      });

      logger.info(`Initialized ${sampleEntities.length} sample entities in memory`);
    }
  }
}

export const entityService = new EntityService();
