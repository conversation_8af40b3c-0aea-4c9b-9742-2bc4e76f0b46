# 游戏逻辑预览工具 (Game Logic Preview)

一个专为游戏设计师打造的集成开发环境，用于快速验证和设计游戏想法，特别是数值循环和玩法机制。

## 项目概述

这个工具允许游戏设计师：
- 通过可视化界面快速定义游戏实体（角色、技能、装备、Buff等）
- 设计复杂的数值关系和逻辑联动
- 进行各种模拟测试（DPS检测、生存能力测试、资源循环等）
- 实时监控和分析模拟数据

## 技术架构

### 前端 (frontend/)
- **技术栈**: React + TypeScript + React Flow
- **功能**: 可视化编辑器、实体管理界面、数据可视化

### 后端 (backend/)
- **技术栈**: Node.js + Express + TypeScript + MongoDB
- **功能**: RESTful API、数据存储、WebSocket实时通信

### 模拟引擎 (simulation-engine/)
- **技术栈**: Python + FastAPI + NumPy
- **功能**: 游戏逻辑模拟、数值计算、事件系统

### 共享类型 (shared/)
- **技术栈**: TypeScript
- **功能**: 前后端共享的类型定义

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- MongoDB 5.0+

### 安装和运行

1. 安装依赖
```bash
# 安装前端依赖
cd frontend && npm install

# 安装后端依赖
cd ../backend && npm install

# 安装Python依赖
cd ../simulation-engine && pip install -r requirements.txt
```

2. 启动服务
```bash
# 启动MongoDB (需要单独安装)
mongod

# 启动后端服务
cd backend && npm run dev

# 启动模拟引擎
cd simulation-engine && python main.py

# 启动前端开发服务器
cd frontend && npm start
```

## 项目结构

```
game-logic-preview/
├── frontend/              # React前端应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── services/      # API服务
│   │   └── types/         # 类型定义
│   ├── public/
│   └── package.json
├── backend/               # Node.js后端服务
│   ├── src/
│   │   ├── routes/        # API路由
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   └── package.json
├── simulation-engine/     # Python模拟引擎
│   ├── src/
│   │   ├── models/        # 数据模型
│   │   ├── engine/        # 模拟引擎核心
│   │   ├── api/           # FastAPI接口
│   │   └── utils/         # 工具函数
│   ├── main.py
│   └── requirements.txt
├── shared/                # 共享类型定义
│   └── types/
├── docs/                  # 项目文档
└── README.md
```

## 开发指南

详细的开发指南请参考 `docs/` 目录下的文档。

## 许可证

MIT License
