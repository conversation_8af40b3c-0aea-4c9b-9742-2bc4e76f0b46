import React from 'react';
import {
  Box,
  <PERSON>lbar,
  IconButton,
  Tooltip,
  Divider,
  ButtonGroup,
  Typography,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Save,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  CenterFocusStrong,
  GridOn,
  GridOff,
  Visibility,
  VisibilityOff,
  PlayArrow,
  Stop,
  Settings,
  Download,
  Upload,
  Delete,
  SelectAll,
  ContentCopy,
  ContentPaste,
  AlignHorizontalCenter,
  AlignVerticalCenter,
  MoreVert,
  Assessment,
} from '@mui/icons-material';

interface EditorToolbarProps {
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onFitView?: () => void;
  onToggleGrid?: () => void;
  onToggleMinimap?: () => void;
  onToggleResults?: () => void;
  onExecute?: () => void;
  onStopExecution?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  onSelectAll?: () => void;
  onCopy?: () => void;
  onPaste?: () => void;
  onDelete?: () => void;
  onAlignHorizontal?: () => void;
  onAlignVertical?: () => void;
  isExecuting?: boolean;
  showGrid?: boolean;
  showMinimap?: boolean;
  showResults?: boolean;
  canUndo?: boolean;
  canRedo?: boolean;
  selectedNodesCount?: number;
  totalNodesCount?: number;
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  onSave,
  onUndo,
  onRedo,
  onZoomIn,
  onZoomOut,
  onFitView,
  onToggleGrid,
  onToggleMinimap,
  onToggleResults,
  onExecute,
  onStopExecution,
  onExport,
  onImport,
  onSelectAll,
  onCopy,
  onPaste,
  onDelete,
  onAlignHorizontal,
  onAlignVertical,
  isExecuting = false,
  showGrid = true,
  showMinimap = true,
  showResults = false,
  canUndo = false,
  canRedo = false,
  selectedNodesCount = 0,
  totalNodesCount = 0,
}) => {
  const [moreMenuAnchor, setMoreMenuAnchor] = React.useState<null | HTMLElement>(null);

  const handleMoreMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMoreMenuAnchor(event.currentTarget);
  };

  const handleMoreMenuClose = () => {
    setMoreMenuAnchor(null);
  };

  return (
    <Box sx={{ 
      borderBottom: 1, 
      borderColor: 'divider',
      backgroundColor: 'background.paper',
      zIndex: 1000,
    }}>
      <Toolbar variant="dense" sx={{ minHeight: 48, px: 2 }}>
        {/* 文件操作 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title="保存 (Ctrl+S)">
            <IconButton onClick={onSave}>
              <Save />
            </IconButton>
          </Tooltip>
          <Tooltip title="导入">
            <IconButton onClick={onImport}>
              <Upload />
            </IconButton>
          </Tooltip>
          <Tooltip title="导出">
            <IconButton onClick={onExport}>
              <Download />
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 编辑操作 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title="撤销 (Ctrl+Z)">
            <span>
              <IconButton onClick={onUndo} disabled={!canUndo}>
                <Undo />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="重做 (Ctrl+Y)">
            <span>
              <IconButton onClick={onRedo} disabled={!canRedo}>
                <Redo />
              </IconButton>
            </span>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 选择和编辑 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title="全选 (Ctrl+A)">
            <IconButton onClick={onSelectAll}>
              <SelectAll />
            </IconButton>
          </Tooltip>
          <Tooltip title="复制 (Ctrl+C)">
            <span>
              <IconButton onClick={onCopy} disabled={selectedNodesCount === 0}>
                <ContentCopy />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="粘贴 (Ctrl+V)">
            <IconButton onClick={onPaste}>
              <ContentPaste />
            </IconButton>
          </Tooltip>
          <Tooltip title="删除 (Delete)">
            <span>
              <IconButton onClick={onDelete} disabled={selectedNodesCount === 0}>
                <Delete />
              </IconButton>
            </span>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 对齐工具 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title="水平对齐">
            <span>
              <IconButton onClick={onAlignHorizontal} disabled={selectedNodesCount < 2}>
                <AlignHorizontalCenter />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="垂直对齐">
            <span>
              <IconButton onClick={onAlignVertical} disabled={selectedNodesCount < 2}>
                <AlignVerticalCenter />
              </IconButton>
            </span>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 视图控制 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title="放大">
            <IconButton onClick={onZoomIn}>
              <ZoomIn />
            </IconButton>
          </Tooltip>
          <Tooltip title="缩小">
            <IconButton onClick={onZoomOut}>
              <ZoomOut />
            </IconButton>
          </Tooltip>
          <Tooltip title="适应视图">
            <IconButton onClick={onFitView}>
              <CenterFocusStrong />
            </IconButton>
          </Tooltip>
          <Tooltip title={showGrid ? "隐藏网格" : "显示网格"}>
            <IconButton onClick={onToggleGrid}>
              {showGrid ? <GridOff /> : <GridOn />}
            </IconButton>
          </Tooltip>
          <Tooltip title={showMinimap ? "隐藏小地图" : "显示小地图"}>
            <IconButton onClick={onToggleMinimap}>
              {showMinimap ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </Tooltip>
          <Tooltip title={showResults ? "隐藏结果面板" : "显示结果面板"}>
            <IconButton onClick={onToggleResults}>
              <Assessment />
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* 执行控制 */}
        <ButtonGroup size="small" sx={{ mr: 2 }}>
          <Tooltip title={isExecuting ? "停止执行" : "开始执行"}>
            <IconButton 
              onClick={isExecuting ? onStopExecution : onExecute}
              color={isExecuting ? "error" : "primary"}
            >
              {isExecuting ? <Stop /> : <PlayArrow />}
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        {/* 弹性空间 */}
        <Box sx={{ flexGrow: 1 }} />

        {/* 状态信息 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2 }}>
          <Typography variant="caption" color="text.secondary">
            节点: {totalNodesCount}
          </Typography>
          {selectedNodesCount > 0 && (
            <Chip
              label={`已选择 ${selectedNodesCount}`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
          {isExecuting && (
            <Chip
              label="执行中"
              size="small"
              color="success"
              variant="filled"
            />
          )}
        </Box>

        {/* 更多选项 */}
        <Tooltip title="更多选项">
          <IconButton onClick={handleMoreMenuOpen}>
            <MoreVert />
          </IconButton>
        </Tooltip>

        <Menu
          anchorEl={moreMenuAnchor}
          open={Boolean(moreMenuAnchor)}
          onClose={handleMoreMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={() => { handleMoreMenuClose(); /* 打开设置 */ }}>
            <ListItemIcon>
              <Settings fontSize="small" />
            </ListItemIcon>
            <ListItemText>设置</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { handleMoreMenuClose(); /* 显示快捷键 */ }}>
            <ListItemIcon>
              <Settings fontSize="small" />
            </ListItemIcon>
            <ListItemText>快捷键</ListItemText>
          </MenuItem>
        </Menu>
      </Toolbar>
    </Box>
  );
};

export default EditorToolbar;
