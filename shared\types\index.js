"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TriggerType = exports.NodeType = exports.EntityType = void 0;
var EntityType;
(function (EntityType) {
    EntityType["CHARACTER"] = "CHARACTER";
    EntityType["SKILL"] = "SKILL";
    EntityType["EQUIPMENT"] = "EQUIPMENT";
    EntityType["BUFF"] = "BUFF";
    EntityType["MONSTER"] = "MONSTER";
    EntityType["ITEM"] = "ITEM";
    EntityType["CONTAINER"] = "CONTAINER";
    EntityType["GRID"] = "GRID";
})(EntityType || (exports.EntityType = EntityType = {}));
var NodeType;
(function (NodeType) {
    NodeType["TRIGGER"] = "TRIGGER";
    NodeType["CONDITION"] = "CONDITION";
    NodeType["EFFECT"] = "EFFECT";
    NodeType["CALCULATION"] = "CALCULATION";
    NodeType["PROBABILITY"] = "PROBABILITY";
    NodeType["DELAY"] = "DELAY";
    NodeType["VARIABLE"] = "VARIABLE";
    NodeType["CONSTANT"] = "CONSTANT";
})(NodeType || (exports.NodeType = NodeType = {}));
var TriggerType;
(function (TriggerType) {
    TriggerType["ON_ATTACK"] = "ON_ATTACK";
    TriggerType["ON_DAMAGE"] = "ON_DAMAGE";
    TriggerType["ON_HEAL"] = "ON_HEAL";
    TriggerType["ON_BUFF_APPLY"] = "ON_BUFF_APPLY";
    TriggerType["ON_BUFF_REMOVE"] = "ON_BUFF_REMOVE";
    TriggerType["ON_DEATH"] = "ON_DEATH";
    TriggerType["ON_TURN_START"] = "ON_TURN_START";
    TriggerType["ON_TURN_END"] = "ON_TURN_END";
    TriggerType["ON_COOLDOWN_READY"] = "ON_COOLDOWN_READY";
})(TriggerType || (exports.TriggerType = TriggerType = {}));
//# sourceMappingURL=index.js.map