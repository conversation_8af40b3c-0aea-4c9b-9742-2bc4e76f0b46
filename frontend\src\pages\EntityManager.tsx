import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';

import { Entity, EntityType, EntityFormData } from '../types';
import { entityApi } from '../services/api';

const EntityManager: React.FC = () => {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingEntity, setEditingEntity] = useState<Entity | null>(null);
  const [formData, setFormData] = useState<EntityFormData>({
    name: '',
    type: EntityType.CHARACTER,
    description: '',
    attributes: {},
    tags: [],
  });

  // 加载实体列表
  const loadEntities = async () => {
    try {
      setLoading(true);
      const response = await entityApi.getAll();
      setEntities(response.entities);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load entities');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEntities();
  }, []);

  // 打开创建对话框
  const handleCreate = () => {
    setEditingEntity(null);
    setFormData({
      name: '',
      type: EntityType.CHARACTER,
      description: '',
      attributes: {},
      tags: [],
    });
    setDialogOpen(true);
  };

  // 打开编辑对话框
  const handleEdit = (entity: Entity) => {
    setEditingEntity(entity);
    setFormData({
      name: entity.name,
      type: entity.type,
      description: entity.description || '',
      attributes: entity.attributes,
      tags: entity.tags,
    });
    setDialogOpen(true);
  };

  // 保存实体
  const handleSave = async () => {
    try {
      if (editingEntity) {
        // 更新现有实体
        const updateData = {
          ...formData,
          logic: editingEntity.logic,
          connections: editingEntity.connections,
        };
        await entityApi.update(editingEntity.id, updateData);
      } else {
        // 创建新实体
        const createData = {
          ...formData,
          logic: [],
          connections: [],
        };
        await entityApi.create(createData);
      }
      setDialogOpen(false);
      loadEntities();
    } catch (err: any) {
      setError(err.message || 'Failed to save entity');
    }
  };

  // 删除实体
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this entity?')) {
      try {
        await entityApi.delete(id);
        loadEntities();
      } catch (err: any) {
        setError(err.message || 'Failed to delete entity');
      }
    }
  };

  // 复制实体
  const handleDuplicate = async (id: string) => {
    try {
      await entityApi.duplicate(id);
      loadEntities();
    } catch (err: any) {
      setError(err.message || 'Failed to duplicate entity');
    }
  };

  // 获取实体类型的颜色
  const getEntityTypeColor = (type: EntityType) => {
    const colors: Record<EntityType, string> = {
      [EntityType.CHARACTER]: 'primary',
      [EntityType.SKILL]: 'secondary',
      [EntityType.EQUIPMENT]: 'success',
      [EntityType.BUFF]: 'warning',
      [EntityType.MONSTER]: 'error',
      [EntityType.ITEM]: 'info',
      [EntityType.CONTAINER]: 'default',
      [EntityType.GRID]: 'default',
    };
    return colors[type] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Entity Manager
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreate}
        >
          Create Entity
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {entities.map((entity) => (
          <Grid item xs={12} sm={6} md={4} key={entity.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                  <Typography variant="h6" component="h2" noWrap>
                    {entity.name}
                  </Typography>
                  <Chip
                    label={entity.type}
                    color={getEntityTypeColor(entity.type) as any}
                    size="small"
                  />
                </Box>
                
                {entity.description && (
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {entity.description}
                  </Typography>
                )}

                <Box mb={1}>
                  <Typography variant="subtitle2" gutterBottom>
                    Attributes: {Object.keys(entity.attributes).length}
                  </Typography>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags: {entity.tags.length}
                  </Typography>
                </Box>

                {entity.tags.length > 0 && (
                  <Box>
                    {entity.tags.slice(0, 3).map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag.name}
                        size="small"
                        variant="outlined"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                    {entity.tags.length > 3 && (
                      <Chip
                        label={`+${entity.tags.length - 3} more`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>
                )}
              </CardContent>
              
              <CardActions>
                <IconButton size="small" onClick={() => handleEdit(entity)}>
                  <EditIcon />
                </IconButton>
                <IconButton size="small" onClick={() => handleDuplicate(entity.id)}>
                  <CopyIcon />
                </IconButton>
                <IconButton size="small" onClick={() => handleDelete(entity.id)}>
                  <DeleteIcon />
                </IconButton>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 创建/编辑对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingEntity ? 'Edit Entity' : 'Create Entity'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            
            <FormControl fullWidth margin="normal">
              <InputLabel>Type</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as EntityType })}
                label="Type"
              >
                {Object.values(EntityType).map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              margin="normal"
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingEntity ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EntityManager;
