/**
 * Simulation routes
 */

import { Router } from 'express';
import axios from 'axios';
import { asyncHandler, createError } from '../utils/errorHandler';
import { logger } from '../utils/logger';
import { Simulation } from '../../../shared/types';

const router = Router();

const SIMULATION_ENGINE_URL = process.env.SIMULATION_ENGINE_URL || 'http://localhost:8000';

// Get all simulations
router.get('/', asyncHandler(async (req, res) => {
  try {
    const response = await axios.get(`${SIMULATION_ENGINE_URL}/api/v1/simulations`);
    res.json(response.data);
  } catch (error) {
    logger.error('Failed to fetch simulations from engine:', error);
    throw createError('Failed to fetch simulations', 500);
  }
}));

// Get simulation by ID
router.get('/:id', asyncHandler(async (req, res) => {
  try {
    const response = await axios.get(`${SIMULATION_ENGINE_URL}/api/v1/simulations/${req.params.id}`);
    res.json(response.data);
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw createError('Simulation not found', 404);
    }
    logger.error('Failed to fetch simulation from engine:', error);
    throw createError('Failed to fetch simulation', 500);
  }
}));

// Create new simulation
router.post('/', asyncHandler(async (req, res) => {
  const simulationData: Simulation = req.body;

  try {
    const response = await axios.post(`${SIMULATION_ENGINE_URL}/api/v1/simulations`, simulationData);
    logger.info(`Created simulation: ${simulationData.name} (${simulationData.id})`);
    res.status(201).json(response.data);
  } catch (error) {
    logger.error('Failed to create simulation in engine:', error);
    throw createError('Failed to create simulation', 500);
  }
}));

// Run simulation
router.post('/:id/run', asyncHandler(async (req, res) => {
  try {
    const response = await axios.post(`${SIMULATION_ENGINE_URL}/api/v1/simulations/${req.params.id}/run`);
    logger.info(`Started simulation: ${req.params.id}`);
    res.json(response.data);
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw createError('Simulation not found', 404);
    }
    logger.error('Failed to run simulation:', error);
    throw createError('Failed to run simulation', 500);
  }
}));

// Get simulation results
router.get('/:id/results', asyncHandler(async (req, res) => {
  try {
    const response = await axios.get(`${SIMULATION_ENGINE_URL}/api/v1/simulations/${req.params.id}/results`);
    res.json(response.data);
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw createError('Simulation not found', 404);
    }
    logger.error('Failed to fetch simulation results:', error);
    throw createError('Failed to fetch simulation results', 500);
  }
}));

// Test connection to simulation engine
router.get('/test/connection', asyncHandler(async (req, res) => {
  try {
    const response = await axios.get(`${SIMULATION_ENGINE_URL}/api/v1/test`);
    res.json({
      status: 'connected',
      engine_response: response.data
    });
  } catch (error) {
    logger.error('Failed to connect to simulation engine:', error);
    throw createError('Simulation engine is not available', 503);
  }
}));

export default router;
